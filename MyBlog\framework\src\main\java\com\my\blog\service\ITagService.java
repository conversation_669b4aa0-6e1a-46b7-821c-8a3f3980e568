package com.my.blog.service;

import com.my.blog.domain.ResponseResult;
import com.my.blog.domain.dto.TagListDto;
import com.my.blog.domain.entity.Tag;
import com.baomidou.mybatisplus.extension.service.IService;
import com.my.blog.domain.vo.PageVo;
import com.my.blog.domain.vo.TagVo;

import java.util.List;

/**
 * <p>
 * 标签 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-21
 */
public interface ITagService extends IService<Tag> {

    ResponseResult<PageVo> pageTagList(Integer pageNum, Integer pageSize, TagListDto tagListDto);

    ResponseResult<Tag> addTag(Tag tag);

    ResponseResult<Tag> deleteTag(Long id);

    ResponseResult<Tag> getTag(Long id);

    ResponseResult<Tag> updateTag(Tag tag);


    List<TagVo> listAllTag();
}
