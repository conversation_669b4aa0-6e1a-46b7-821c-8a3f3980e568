package com.my.blog.exception.handler;

import com.alibaba.fastjson.JSON;
import com.my.blog.domain.ResponseResult;
import com.my.blog.enums.AppHttpCodeEnum;
import com.my.blog.utils.WebUtils;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.web.access.AccessDeniedHandler;
import org.springframework.stereotype.Component;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
//AccessDeniedHandler 授权失败处理器
@Component
public class AccessDeniedHandlerImpl implements AccessDeniedHandler {
 @Override
 public void handle(HttpServletRequest request, HttpServletResponse response,
                    AccessDeniedException accessDeniedException) throws IOException, ServletException
{
 accessDeniedException.printStackTrace();
 ResponseResult result =
ResponseResult.errorResult(AppHttpCodeEnum.NO_OPERATOR_AUTH);
 //响应给前端
 WebUtils.renderString(response, JSON.toJSONString(result));
 }
}