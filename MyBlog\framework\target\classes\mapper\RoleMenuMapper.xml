<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.my.blog.dao.RoleMenuMapper">

    <select id="selectMenuIdsByRoleId" resultType="java.lang.Long">
        select menu_id
        from sys_role_menu
        where role_id = #{roleId}
    </select>

    <delete id="deleteByRoleId">
        delete from sys_role_menu where role_id = #{roleId}
    </delete>

</mapper>
