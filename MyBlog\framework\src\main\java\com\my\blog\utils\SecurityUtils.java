package com.my.blog.utils;

import com.my.blog.domain.entity.LoginUser;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;

public class SecurityUtils
{
/**
* 获取用户
**/
public static LoginUser getLoginUser()
{
    Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
return (LoginUser) authentication.getPrincipal();
}
/**
* 获取Authentication
*/
public static Authentication getAuthentication() {
return SecurityContextHolder.getContext().getAuthentication();
}

public static Boolean isAdmin(){
Long userId = getLoginUser().getUser().getId();
return userId != null && 1L == userId;
}

public static Long getUserId() {
return getLoginUser().getUser().getId();
}
}