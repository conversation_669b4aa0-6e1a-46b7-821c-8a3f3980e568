package com.my.blog.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.my.blog.domain.ResponseResult;
import com.my.blog.domain.dto.AddArticleDto;
import com.my.blog.domain.entity.Article;

/**
 * <p>
 * 文章表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-19
 */
public interface IArticleService extends IService<Article> {

    ResponseResult hotArticleList();

    ResponseResult articleList(Integer pageNum, Integer pageSize, Long categotyId);

    ResponseResult getArticleDetail(Long id);

    ResponseResult updateViewCount(Long id);


    ResponseResult getAdminArticleList(Integer pageNum, Integer pageSize, String title, String summary);

    ResponseResult addArticle(AddArticleDto article);

    ResponseResult getAdminArticleDetail(Long id);

    ResponseResult updateArticle(AddArticleDto article);

    ResponseResult deleteArticle(Long id);
}
