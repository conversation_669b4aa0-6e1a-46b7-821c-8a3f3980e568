package com.my.blog.controller;

import com.my.blog.domain.ResponseResult;
import com.my.blog.domain.dto.AddUserDto;
import com.my.blog.domain.entity.User;
import com.my.blog.service.IUserService;
import com.my.blog.service.IRoleService;
import com.my.blog.service.IUserRoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 系统用户管理控制器
 */
@RestController
@RequestMapping("/system/user")
public class SystemUserController {

    @Autowired
    private IUserService userService;

    @Autowired
    private IRoleService roleService;

    @Autowired
    private IUserRoleService userRoleService;

    /**
     * 用户列表
     */
    @GetMapping("/list")
    public ResponseResult getUserList(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String userName,
            @RequestParam(required = false) String phonenumber,
            @RequestParam(required = false) String status) {
        return userService.getUserList(pageNum, pageSize, userName, phonenumber, status);
    }

    /**
     * 新增用户
     */
    @PostMapping
    public ResponseResult addUser(@RequestBody AddUserDto addUserDto) {
        return userService.addUser(addUserDto.getUser(), addUserDto.getRoleIds());
    }

    /**
     * 删除用户
     */
    @DeleteMapping("/{id}")
    public ResponseResult deleteUser(@PathVariable Long id) {
        return userService.deleteUser(id);
    }

    /**
     * 根据id查询用户信息（包含角色信息）
     */
    @GetMapping("/{id}")
    public ResponseResult getUserById(@PathVariable Long id) {
        ResponseResult userResult = userService.getUserById(id);
        if (userResult.getCode() == 200) {
            // 查询用户的角色ID列表
            List<Long> roleIds = userRoleService.selectRoleIdsByUserId(id);
            // 可以创建一个包含用户信息和角色ID的VO
            return ResponseResult.okResult(new AddUserDto((User) userResult.getData(), roleIds));
        }
        return userResult;
    }

    /**
     * 更新用户信息
     */
    @PutMapping
    public ResponseResult updateUser(@RequestBody AddUserDto addUserDto) {
        return userService.updateSystemUser(addUserDto.getUser(), addUserDto.getRoleIds());
    }

    /**
     * 查询所有角色列表（用于新增用户时选择角色）
     */
    @GetMapping("/listAllRole")
    public ResponseResult listAllRole() {
        return ResponseResult.okResult(roleService.list());
    }
}
