{"name": "normalize-path", "description": "Normalize file path slashes to be unix-like forward slashes. Also condenses repeat slashes to a single slash and removes and trailing slashes.", "version": "1.0.0", "homepage": "https://github.com/jonschlinkert/normalize-path", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git://github.com/jonschlinkert/normalize-path.git"}, "bugs": {"url": "https://github.com/jonschlinkert/normalize-path/issues"}, "license": {"type": "MIT", "url": "https://github.com/jonschlinkert/normalize-path/blob/master/LICENSE-MIT"}, "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha -R spec"}, "devDependencies": {"benchmarked": "^0.1.1", "mocha": "*"}, "keywords": ["file", "filepath", "fp", "normalize", "path", "slash", "slashes", "trailing", "unix"]}