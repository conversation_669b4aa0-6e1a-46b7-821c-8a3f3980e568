# window-size [![NPM version](https://badge.fury.io/js/window-size.png)](http://badge.fury.io/js/window-size)

> Reliable way to to get the height and width of the terminal/console in a node.js environment.

## Install

### [npm](npmjs.org)

```bash
npm i window-size --save
```

```javascript
var size = require('window-size');
size.height; // "80" (rows)
size.width; // "25" (columns)
```

## Author

+ [github/jonschlinkert](https://github.com/jonschlinkert)
+ [twitter/jonschlinkert](http://twitter.com/jonschlinkert)

## License
Copyright (c) 2014 Jon <PERSON>
Licensed under the MIT license.