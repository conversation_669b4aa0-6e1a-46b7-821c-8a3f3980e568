<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="c09da832-7930-4c9a-bcab-d4e8c099bc42" name="更改" comment="version 1">
      <change beforePath="$PROJECT_DIR$/.idea/misc.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/misc.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Blog/src/main/resources/application.yml" beforeDir="false" afterPath="$PROJECT_DIR$/Blog/src/main/resources/application.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/admin/src/main/java/com/my/blog/controller/AdminArticleController.java" beforeDir="false" afterPath="$PROJECT_DIR$/admin/src/main/java/com/my/blog/controller/AdminArticleController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/admin/src/main/java/com/my/blog/controller/ArticleController.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/admin/src/main/java/com/my/blog/controller/CategoryController.java" beforeDir="false" afterPath="$PROJECT_DIR$/admin/src/main/java/com/my/blog/controller/CategoryController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/admin/src/main/resources/application.yml" beforeDir="false" afterPath="$PROJECT_DIR$/admin/src/main/resources/application.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/framework/src/main/java/com/my/blog/service/IArticleService.java" beforeDir="false" afterPath="$PROJECT_DIR$/framework/src/main/java/com/my/blog/service/IArticleService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/framework/src/main/java/com/my/blog/service/ICategoryService.java" beforeDir="false" afterPath="$PROJECT_DIR$/framework/src/main/java/com/my/blog/service/ICategoryService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/framework/src/main/java/com/my/blog/service/ILinkService.java" beforeDir="false" afterPath="$PROJECT_DIR$/framework/src/main/java/com/my/blog/service/ILinkService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/framework/src/main/java/com/my/blog/service/IMenuService.java" beforeDir="false" afterPath="$PROJECT_DIR$/framework/src/main/java/com/my/blog/service/IMenuService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/framework/src/main/java/com/my/blog/service/IRoleService.java" beforeDir="false" afterPath="$PROJECT_DIR$/framework/src/main/java/com/my/blog/service/IRoleService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/framework/src/main/java/com/my/blog/service/IUserService.java" beforeDir="false" afterPath="$PROJECT_DIR$/framework/src/main/java/com/my/blog/service/IUserService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/framework/src/main/java/com/my/blog/service/impl/ArticleServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/framework/src/main/java/com/my/blog/service/impl/ArticleServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/framework/src/main/java/com/my/blog/service/impl/BlogLoginServiceImpl.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/framework/src/main/java/com/my/blog/service/impl/CategoryServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/framework/src/main/java/com/my/blog/service/impl/CategoryServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/framework/src/main/java/com/my/blog/service/impl/LinkServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/framework/src/main/java/com/my/blog/service/impl/LinkServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/framework/src/main/java/com/my/blog/service/impl/MenuServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/framework/src/main/java/com/my/blog/service/impl/MenuServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/framework/src/main/java/com/my/blog/service/impl/RoleServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/framework/src/main/java/com/my/blog/service/impl/RoleServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/framework/src/main/java/com/my/blog/service/impl/UserServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/framework/src/main/java/com/my/blog/service/impl/UserServiceImpl.java" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Interface" />
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 2
}</component>
  <component name="ProjectId" id="2xqkb7ZWT0ZlytPNAD93Ja5mLWN" />
  <component name="ProjectLevelVcsManager" settingsEditedManually="true" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="openDirectoriesWithSingleClick" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.OpenProjectViewOnStart": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "Spring Boot.AdminApplication.executor": "Run",
    "Spring Boot.BlogApplication (1).executor": "Run",
    "Spring Boot.BlogApplication.executor": "Run",
    "git-widget-placeholder": "main",
    "jdk.selected.JAVA_MODULE": "corretto-18 (2)",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "F:/keshe/MyBlog",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "settings.editor.selected.configurable": "preferences.pluginManager",
    "vue.rearranger.settings.migration": "true"
  },
  "keyToStringList": {
    "DatabaseDriversLRU": [
      "mysql"
    ]
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="F:\keshe\MyBlog\framework\src\main\java\com\my\blog" />
      <recent name="F:\keshe\MyBlog\Blog\src\main\java\com\my\blog" />
      <recent name="F:\keshe\MyBlog\framework\src\main\java\org\example" />
    </key>
  </component>
  <component name="RunManager" selected="Spring Boot.AdminApplication">
    <configuration name="AdminApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" temporary="true" nameIsGenerated="true">
      <module name="admin" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.my.blog.AdminApplication" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.my.blog.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="BlogApplication (1)" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" temporary="true" nameIsGenerated="true">
      <module name="blog" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.my.blog.BlogApplication" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.my.blog.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="BlogApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" temporary="true" nameIsGenerated="true">
      <module name="Blog" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.my.blog.BlogApplication" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.my.blog.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Spring Boot.AdminApplication" />
        <item itemvalue="Spring Boot.BlogApplication (1)" />
        <item itemvalue="Spring Boot.BlogApplication" />
      </list>
    </recent_temporary>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="c09da832-7930-4c9a-bcab-d4e8c099bc42" name="更改" comment="" />
      <created>1748673062927</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1748673062927</updated>
      <workItem from="1748673063999" duration="9757000" />
      <workItem from="1748745889561" duration="13254000" />
      <workItem from="1748868760622" duration="10317000" />
      <workItem from="1749035537491" duration="2124000" />
      <workItem from="1749104873695" duration="2089000" />
      <workItem from="1749124704417" duration="7044000" />
      <workItem from="1750251439499" duration="5765000" />
      <workItem from="1750330607420" duration="6889000" />
      <workItem from="1750481506056" duration="5863000" />
      <workItem from="1750579374116" duration="517000" />
      <workItem from="1750580030789" duration="3382000" />
    </task>
    <task id="LOCAL-00001" summary="已经写到6 博客前台评论模块 视频第60分钟">
      <option name="closed" value="true" />
      <created>1749131927979</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1749131927979</updated>
    </task>
    <task id="LOCAL-00002" summary="已经写到6 博客前台评论模块 视频第60分钟">
      <option name="closed" value="true" />
      <created>1749131963356</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1749131963356</updated>
    </task>
    <task id="LOCAL-00003" summary="已经写到6 博客前台评论模块 视频第60分钟">
      <option name="closed" value="true" />
      <created>1750257130306</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1750257130306</updated>
    </task>
    <option name="localTasksCounter" value="4" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="已经写到6 博客前台评论模块 视频第60分钟" />
    <MESSAGE value="准备博客前台 7 开始写" />
    <MESSAGE value="version 1" />
    <option name="LAST_COMMIT_MESSAGE" value="version 1" />
  </component>
</project>