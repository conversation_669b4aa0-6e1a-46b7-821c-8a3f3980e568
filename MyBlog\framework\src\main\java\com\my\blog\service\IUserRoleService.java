package com.my.blog.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.my.blog.domain.entity.UserRole;

import java.util.List;

/**
 * <p>
 * 用户角色关联表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
public interface IUserRoleService extends IService<UserRole> {

    /**
     * 根据用户ID查询角色ID列表
     */
    List<Long> selectRoleIdsByUserId(Long userId);

    /**
     * 保存用户角色关联
     */
    void saveUserRoles(Long userId, List<Long> roleIds);

    /**
     * 删除用户的所有角色关联
     */
    void deleteByUserId(Long userId);
}
