<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.my.blog.dao.MenuMapper">

    <select id="selectPermsByUserId" resultType="java.lang.String">
        select sm.perms
        from sys_user_role sur
                 left join sys_role_menu srm on sur.role_id = srm.role_id
                 left join sys_menu sm on srm.menu_id = sm.id
        where sur.user_id = #{id}
          and sm.menu_type in ('C', 'F')
          and sm.status = 0
          and sm.del_flag = 0
    </select>

    <select id="selectRouterMenuByUserId"
            resultType="com.my.blog.domain.entity.Menu">
        select *
        from sys_user_role sur
                 left join sys_role_menu srm on sur.role_id = srm.role_id
                 left join sys_menu sm on srm.menu_id = sm.id
        where sur.user_id = #{userId}
          and sm.menu_type in ('C', 'M')
          and sm.status = 0
          and sm.del_flag = 0
        order by sm.parent_id, sm.order_num
    </select>

</mapper>