package com.my.blog.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.my.blog.domain.ResponseResult;
import com.my.blog.domain.entity.Role;

import java.util.List;


/**
 * <p>
 * 角色信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-19
 */
public interface IRoleService extends IService<Role> {

    List<String> selectRoleKeyByUserId(Long id);

    // 角色管理功能
    ResponseResult getRoleList(Integer pageNum, Integer pageSize, String roleName, String status);

    ResponseResult addRole(Role role);

    ResponseResult addRoleWithMenus(Role role, List<Long> menuIds);

    ResponseResult updateRole(Role role);

    ResponseResult updateRoleWithMenus(Role role, List<Long> menuIds);

    ResponseResult deleteRole(Long id);

    ResponseResult getRoleById(Long id);

    ResponseResult changeStatus(Long roleId, String status);
}
