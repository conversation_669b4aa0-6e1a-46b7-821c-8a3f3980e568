package com.my.blog.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.my.blog.constant.SystemConstants;
import com.my.blog.dao.ArticleMapper;
import com.my.blog.dao.CategoryMapper;
import com.my.blog.domain.ResponseResult;
import com.my.blog.domain.entity.Article;
import com.my.blog.domain.entity.Category;
import com.my.blog.domain.vo.CategoryVo;
import com.my.blog.domain.vo.PageVo;
import com.my.blog.service.ICategoryService;
import com.my.blog.utils.BeanCopyUtils;
import com.my.blog.utils.SecurityUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 分类表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-19
 */
@Service
public class CategoryServiceImpl extends ServiceImpl<CategoryMapper, Category> implements ICategoryService {
    @Autowired
    private ArticleMapper articleMapper;
    @Autowired
    private CategoryMapper categoryMapper;

    @Override
    public ResponseResult getCategoryList() {


//        只展示有发布的正式文章分类
//        查询已经发布的文章

        LambdaQueryWrapper<Article> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Article::getStatus, SystemConstants.ARTICLE_STATUS_NORMAL);

        List<Article> articles = articleMapper.selectList(queryWrapper);


//        获取已经发布的文章id并且去重
        Set<Long> categoryIds = articles.stream().map(Article::getCategoryId).collect(Collectors.toSet());

//        根据id查询分类表
        List<Category> categories = categoryMapper.selectBatchIds(categoryIds);

//      封装vo
        ArrayList<CategoryVo> categoryVos = new ArrayList<>();
        for (Category category : categories) {
            CategoryVo categoryVo = new CategoryVo();
            BeanUtils.copyProperties(category, categoryVo);
            categoryVos.add(categoryVo);
        }

        return ResponseResult.okResult(categoryVos);
    }

    @Override
    public List<CategoryVo> listAllCategory() {
        LambdaQueryWrapper<Category> queryWrapper = new LambdaQueryWrapper<>();
        // 查询Status为正常状态的分类
        queryWrapper.eq(Category::getStatus, SystemConstants.NORMAL);
        // 封装为List<CategoryVo>返回
        List<Category> list = list(queryWrapper);
        List<CategoryVo> categoryVos = BeanCopyUtils.copyList(list,
                CategoryVo.class);
        return categoryVos;
    }

    @Override
    public ResponseResult getAdminCategoryList(Integer pageNum, Integer pageSize, String name, String status) {
        LambdaQueryWrapper<Category> queryWrapper = new LambdaQueryWrapper<>();

        // 构建查询条件
        queryWrapper.like(StringUtils.hasText(name), Category::getName, name)
                   .eq(StringUtils.hasText(status), Category::getStatus, status);

        // 分页查询
        Page<Category> page = new Page<>(pageNum, pageSize);
        page(page, queryWrapper);

        // 转换为PageVo
        PageVo pageVo = new PageVo(page.getRecords(), page.getTotal());
        return ResponseResult.okResult(pageVo);
    }

    @Override
    public ResponseResult addCategory(Category category) {
        // 检查分类名是否已存在
        if (isCategoryNameExist(category.getName())) {
            return ResponseResult.errorResult(500, "分类名已存在");
        }

        // 设置创建者
        category.setCreateBy(SecurityUtils.getUserId());
        category.setStatus(SystemConstants.NORMAL); // 默认正常状态

        // 保存分类
        save(category);

        return ResponseResult.okResult();
    }

    @Override
    public ResponseResult updateCategory(Category category) {
        // 设置更新者
        category.setUpdateBy(SecurityUtils.getUserId());

        // 更新分类信息
        updateById(category);

        return ResponseResult.okResult();
    }

    @Override
    public ResponseResult deleteCategory(Long id) {
        // 检查是否有文章使用该分类
        LambdaQueryWrapper<Article> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Article::getCategoryId, id);
        long count = articleMapper.selectCount(queryWrapper);
        if (count > 0) {
            return ResponseResult.errorResult(500, "该分类下还有文章，不能删除");
        }

        // 删除分类
        removeById(id);

        return ResponseResult.okResult();
    }

    @Override
    public ResponseResult getCategoryById(Long id) {
        Category category = getById(id);
        if (category == null) {
            return ResponseResult.errorResult(500, "分类不存在");
        }
        return ResponseResult.okResult(category);
    }

    private boolean isCategoryNameExist(String name) {
        return count(new LambdaQueryWrapper<Category>()
                .eq(Category::getName, name)) > 0;
    }
}
