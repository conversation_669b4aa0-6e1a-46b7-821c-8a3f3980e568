package com.my.blog.controller;

import com.my.blog.domain.ResponseResult;
import com.my.blog.domain.entity.Link;
import com.my.blog.service.ILinkService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 系统友链管理控制器
 */
@RestController
@RequestMapping("/content/link")
public class SystemLinkController {

    @Autowired
    private ILinkService linkService;

    /**
     * 友链列表
     */
    @GetMapping("/list")
    public ResponseResult getLinkList(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String name,
            @RequestParam(required = false) String status) {
        return linkService.getAdminLinkList(pageNum, pageSize, name, status);
    }

    /**
     * 新增友链
     */
    @PostMapping
    public ResponseResult addLink(@RequestBody Link link) {
        return linkService.addLink(link);
    }

    /**
     * 修改友链
     */
    @PutMapping
    public ResponseResult updateLink(@RequestBody Link link) {
        return linkService.updateLink(link);
    }

    /**
     * 删除友链
     */
    @DeleteMapping("/{id}")
    public ResponseResult deleteLink(@PathVariable Long id) {
        return linkService.deleteLink(id);
    }

    /**
     * 根据id查询友链信息
     */
    @GetMapping("/{id}")
    public ResponseResult getLinkById(@PathVariable Long id) {
        return linkService.getLinkById(id);
    }

    /**
     * 改变友链状态（审核状态）
     */
    @PutMapping("/changeLinkStatus")
    public ResponseResult changeLinkStatus(@RequestBody Link link) {
        return linkService.changeLinkStatus(link.getId(), link.getStatus());
    }
}
