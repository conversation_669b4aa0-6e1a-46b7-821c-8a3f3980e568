package com.my.blog.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.my.blog.constant.SystemConstants;
import com.my.blog.dao.MenuMapper;
import com.my.blog.domain.ResponseResult;
import com.my.blog.domain.entity.Menu;
import com.my.blog.domain.vo.MenuVo;
import com.my.blog.service.IMenuService;
import com.my.blog.utils.BeanCopyUtils;
import com.my.blog.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 菜单权限表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-19
 */
@Service
public class MenuServiceImpl extends ServiceImpl<MenuMapper, Menu> implements IMenuService {

    @Autowired
    private MenuMapper menuMapper;

    @Override
    public List<String> selectPermsByUserId(Long id) {
        // 如果是管理员，返回所有权限标识
        if (id == 1L) {
            LambdaQueryWrapper<Menu> queryWrapper = new LambdaQueryWrapper<Menu>();
            queryWrapper.in(Menu::getMenuType, "C", "F").eq(Menu::getStatus, SystemConstants.STATUS_NORMAL);
            List<Menu> menus = menuMapper.selectList(queryWrapper);
            List<String> perms = menus.stream().map(Menu::getPerms).collect(Collectors.toList());
            return perms;
        }
        // 否则返回用户所具有的权限标识
        List<String> perms = menuMapper.selectPermsByUserId(id);
        return perms;
    }

    @Override
    public List<MenuVo> selectRouterMenuTreeByUserId(Long userId) {

        List<Menu> menus = null;
        // 判断是否是管理员
        if (userId == 1L) {
            LambdaQueryWrapper<Menu> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(Menu::getMenuType, "C", "F")
                    .eq(Menu::getStatus, SystemConstants.STATUS_NORMAL)
                    .orderByAsc(Menu::getParentId, Menu::getOrderNum);
            menus = menuMapper.selectList(queryWrapper);
        } else {
            menus = menuMapper.selectRouterMenuByUserId(userId);
        }

        // 将perm值为null的替换为空字符串
        for (Menu menu : menus) {
            if (menu.getPerms() == null) {
                menu.setPerms("");
            }
        }

        // 做数据封装， 使用MenuVo
        List<MenuVo> menuVos = BeanCopyUtils.copyList(menus, MenuVo.class);

        // 构建父子菜单， 先找一级菜单， 再把子菜单设置到一级菜单的children中
        List<MenuVo> menuTree = buildMenuTree(menuVos);
        return menuTree;
    }

    private List<MenuVo> buildMenuTree(List<MenuVo> menuVos) {
        // 先找一级菜单
        List<MenuVo> menuTree = menuVos.stream().filter(menuVo -> menuVo.getParentId().equals(0L))
                // 把子菜单设置到一级菜单的children中
                .map(menuVo -> setChildrenList(menuVo, menuVos))
                .collect(Collectors.toList());
        return menuTree;
    }

    private MenuVo setChildrenList(MenuVo parent, List<MenuVo> menuVos) {
        // 从menuVos里找到所有parentId等于parent id的菜单， 就是parent的子菜单
        List<MenuVo> childrenList = menuVos.stream().filter(menuVo -> menuVo.getParentId().equals(parent.getId()))
                .collect(Collectors.toList());
        parent.setChildren(childrenList);
        return parent;
    }

    @Override
    public ResponseResult getMenuList(String menuName, String status) {
        LambdaQueryWrapper<Menu> queryWrapper = new LambdaQueryWrapper<>();

        // 构建查询条件
        queryWrapper.like(StringUtils.hasText(menuName), Menu::getMenuName, menuName)
                   .eq(StringUtils.hasText(status), Menu::getStatus, status)
                   .orderByAsc(Menu::getParentId, Menu::getOrderNum);

        List<Menu> menus = list(queryWrapper);
        return ResponseResult.okResult(menus);
    }

    @Override
    public ResponseResult addMenu(Menu menu) {
        // 设置创建者
        menu.setCreateBy(SecurityUtils.getUserId());

        // 保存菜单
        save(menu);

        return ResponseResult.okResult();
    }

    @Override
    public ResponseResult updateMenu(Menu menu) {
        // 不能把父菜单设置为自己
        if (menu.getId().equals(menu.getParentId())) {
            return ResponseResult.errorResult(500, "修改菜单'" + menu.getMenuName() + "'失败，上级菜单不能选择自己");
        }

        // 设置更新者
        menu.setUpdateBy(SecurityUtils.getUserId());

        // 更新菜单信息
        updateById(menu);

        return ResponseResult.okResult();
    }

    @Override
    public ResponseResult deleteMenu(Long id) {
        // 检查是否有子菜单
        LambdaQueryWrapper<Menu> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Menu::getParentId, id);
        long count = count(queryWrapper);
        if (count > 0) {
            return ResponseResult.errorResult(500, "存在子菜单,不允许删除");
        }

        // 删除菜单
        removeById(id);

        return ResponseResult.okResult();
    }

    @Override
    public ResponseResult getMenuById(Long id) {
        Menu menu = getById(id);
        if (menu == null) {
            return ResponseResult.errorResult(500, "菜单不存在");
        }
        return ResponseResult.okResult(menu);
    }

    @Override
    public ResponseResult getMenuTree() {
        // 查询所有菜单
        LambdaQueryWrapper<Menu> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.orderByAsc(Menu::getParentId, Menu::getOrderNum);
        List<Menu> menus = list(queryWrapper);

        // 转换为MenuVo并构建树形结构
        List<MenuVo> menuVos = BeanCopyUtils.copyList(menus, MenuVo.class);
        List<MenuVo> menuTree = buildMenuTree(menuVos);

        return ResponseResult.okResult(menuTree);
    }

}
