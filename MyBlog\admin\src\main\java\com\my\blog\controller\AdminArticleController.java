package com.my.blog.controller;


import com.my.blog.domain.ResponseResult;
import com.my.blog.domain.dto.AddArticleDto;
import com.my.blog.domain.entity.Article;
import com.my.blog.service.IArticleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping("/content/article")
public class AdminArticleController {

    @Resource
    private IArticleService articleService;

    /*
         pageNum: 页码
         pageSize: 每页条数
         title: 文章标题
         summary: 文章摘要
     */
    @PreAuthorize("@permissionService.hasPermission('content:article:list')")
    @GetMapping("/list")
    public ResponseResult getArticleList(Integer pageNum, Integer pageSize,
                                         String title, String summary) {
        ResponseResult articles = articleService.getAdminArticleList(pageNum,
                pageSize, title, summary);
        return articles;
    }

    /**
     * 新增文章
     */
    @PreAuthorize("@permissionService.hasPermission('content:article:writer')")
    @PostMapping
    public ResponseResult addArticle(@RequestBody AddArticleDto addArticleDto) {
        return articleService.addArticle(addArticleDto);
    }

    /**
     * 根据id查询文章详情（用于编辑）
     */
    @PreAuthorize("@permissionService.hasPermission('content:article:query')")
    @GetMapping("/{id}")
    public ResponseResult getArticleDetail(@PathVariable Long id) {
        return articleService.getAdminArticleDetail(id);
    }

    /**
     * 修改文章
     */
    @PreAuthorize("@permissionService.hasPermission('content:article:edit')")
    @PutMapping
    public ResponseResult updateArticle(@RequestBody AddArticleDto addArticleDto) {
        return articleService.updateArticle(addArticleDto);
    }

    /**
     * 删除文章
     */
    @PreAuthorize("@permissionService.hasPermission('content:article:remove')")
    @DeleteMapping("/{id}")
    public ResponseResult deleteArticle(@PathVariable Long id) {
        return articleService.deleteArticle(id);
    }
}
