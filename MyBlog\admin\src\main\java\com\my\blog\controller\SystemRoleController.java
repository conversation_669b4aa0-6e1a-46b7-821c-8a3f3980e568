package com.my.blog.controller;

import com.my.blog.domain.ResponseResult;
import com.my.blog.domain.entity.Role;
import com.my.blog.service.IRoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 系统角色管理控制器
 */
@RestController
@RequestMapping("/system/role")
public class SystemRoleController {

    @Autowired
    private IRoleService roleService;

    /**
     * 角色列表
     */
    @GetMapping("/list")
    public ResponseResult getRoleList(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String roleName,
            @RequestParam(required = false) String status) {
        return roleService.getRoleList(pageNum, pageSize, roleName, status);
    }

    /**
     * 新增角色
     */
    @PostMapping
    public ResponseResult addRole(@RequestBody Role role) {
        return roleService.addRole(role);
    }

    /**
     * 修改角色
     */
    @PutMapping
    public ResponseResult updateRole(@RequestBody Role role) {
        return roleService.updateRole(role);
    }

    /**
     * 删除角色
     */
    @DeleteMapping("/{id}")
    public ResponseResult deleteRole(@PathVariable Long id) {
        return roleService.deleteRole(id);
    }

    /**
     * 根据id查询角色信息
     */
    @GetMapping("/{id}")
    public ResponseResult getRoleById(@PathVariable Long id) {
        return roleService.getRoleById(id);
    }

    /**
     * 改变角色状态
     */
    @PutMapping("/changeStatus")
    public ResponseResult changeStatus(@RequestBody Role role) {
        return roleService.changeStatus(role.getId(), role.getStatus());
    }

    /**
     * 查询所有角色列表（用于分配角色时选择）
     */
    @GetMapping("/listAllRole")
    public ResponseResult listAllRole() {
        return ResponseResult.okResult(roleService.list());
    }
}
