package com.my.blog.controller;

import com.my.blog.domain.ResponseResult;
import com.my.blog.domain.dto.AddRoleDto;
import com.my.blog.domain.entity.Role;
import com.my.blog.service.IRoleMenuService;
import com.my.blog.service.IRoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 系统角色管理控制器
 */
@RestController
@RequestMapping("/system/role")
public class SystemRoleController {

    @Autowired
    private IRoleService roleService;

    @Autowired
    private IRoleMenuService roleMenuService;

    /**
     * 角色列表
     */
    @GetMapping("/list")
    public ResponseResult getRoleList(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String roleName,
            @RequestParam(required = false) String status) {
        return roleService.getRoleList(pageNum, pageSize, roleName, status);
    }

    /**
     * 新增角色
     */
    @PostMapping
    public ResponseResult addRole(@RequestBody AddRoleDto addRoleDto) {
        return roleService.addRoleWithMenus(addRoleDto.getRole(), addRoleDto.getMenuIds());
    }

    /**
     * 修改角色
     */
    @PutMapping
    public ResponseResult updateRole(@RequestBody AddRoleDto addRoleDto) {
        return roleService.updateRoleWithMenus(addRoleDto.getRole(), addRoleDto.getMenuIds());
    }

    /**
     * 删除角色
     */
    @DeleteMapping("/{id}")
    public ResponseResult deleteRole(@PathVariable Long id) {
        return roleService.deleteRole(id);
    }

    /**
     * 根据id查询角色信息
     */
    @GetMapping("/{id}")
    public ResponseResult getRoleById(@PathVariable Long id) {
        ResponseResult roleResult = roleService.getRoleById(id);
        if (roleResult.getCode() == 200) {
            // 查询角色的菜单ID列表
            List<Long> menuIds = roleMenuService.selectMenuIdsByRoleId(id);
            // 创建包含角色信息和菜单ID的DTO
            return ResponseResult.okResult(new AddRoleDto((Role) roleResult.getData(), menuIds));
        }
        return roleResult;
    }

    /**
     * 改变角色状态
     */
    @PutMapping("/changeStatus")
    public ResponseResult changeStatus(@RequestBody Role role) {
        return roleService.changeStatus(role.getId(), role.getStatus());
    }

    /**
     * 查询所有角色列表（用于分配角色时选择）
     */
    @GetMapping("/listAllRole")
    public ResponseResult listAllRole() {
        return ResponseResult.okResult(roleService.list());
    }
}
