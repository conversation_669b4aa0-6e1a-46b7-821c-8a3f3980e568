-- 快速修复admin用户菜单权限
-- 直接复制以下SQL语句到MySQL命令行执行

USE blog;

-- 添加缺失的菜单权限
INSERT IGNORE INTO `sys_menu` VALUES (2029, '文章查询', 2019, 1, '', NULL, 1, 'F', '0', '0', 'content:article:query', '#', NULL, NOW(), NULL, NOW(), '', '0');
INSERT IGNORE INTO `sys_menu` VALUES (2030, '文章新增', 2019, 2, '', NULL, 1, 'F', '0', '0', 'content:article:writer', '#', NULL, NOW(), NULL, NOW(), '', '0');
INSERT IGNORE INTO `sys_menu` VALUES (2031, '文章修改', 2019, 3, '', NULL, 1, 'F', '0', '0', 'content:article:edit', '#', NULL, NOW(), NULL, NOW(), '', '0');
INSERT IGNORE INTO `sys_menu` VALUES (2032, '文章删除', 2019, 4, '', NULL, 1, 'F', '0', '0', 'content:article:remove', '#', NULL, NOW(), NULL, NOW(), '', '0');

-- 为管理员角色添加系统管理菜单权限
INSERT IGNORE INTO `sys_role_menu` (role_id, menu_id) VALUES 
(1, 1),    -- 系统管理目录
(1, 100),  -- 用户管理
(1, 101),  -- 角色管理  
(1, 102),  -- 菜单管理
(1, 1001), (1, 1002), (1, 1003), (1, 1004), (1, 1005), (1, 1006), (1, 1007), -- 用户管理权限
(1, 1008), (1, 1009), (1, 1010), (1, 1011), (1, 1012), -- 角色管理权限
(1, 1013), (1, 1014), (1, 1015), (1, 1016); -- 菜单管理权限

-- 确保admin用户关联到管理员角色
INSERT IGNORE INTO sys_user_role (user_id, role_id) 
SELECT u.id, 1 FROM sys_user u WHERE u.user_name = 'admin';

SELECT '配置完成！请重启后端服务并重新登录。' as message;
