package com.my.blog.controller;

import com.my.blog.annotation.SystemLog;
import com.my.blog.domain.ResponseResult;
import com.my.blog.domain.entity.User;
import com.my.blog.service.IUserService;
import com.qiniu.rtc.model.RoomResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 用户表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-19
 */
@Controller
@RequestMapping("/user")
public class UserController {
    @Autowired
    private IUserService userService;
    @GetMapping("/userInfo")
    @ResponseBody
    public ResponseResult userInfo() {
        return userService.userInfo();
    }


    @PutMapping("/userInfo")
    @ResponseBody
    @SystemLog(businessName = "更新用户信息")
    public ResponseResult updateUser(@RequestBody User user) {
        return userService.updateUser(user);

    }
    @PostMapping("/register")
    @ResponseBody
    public ResponseResult register(@RequestBody User user) {
        return userService.register(user);
    }
}
