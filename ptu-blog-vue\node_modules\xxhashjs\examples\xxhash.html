<html>
<head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"></head>
<body>
	<div>
		Result:
		<div id='input'></div>
		<div id='seed'></div>
		<div id='xxhash'></div>
		<div id='xxhash64'></div>
	</div>
	<script type="text/javascript" src="../build/xxhash.min.js"></script>
	<script type="text/javascript">
		var input='heiå'
		var seed = '0'
		var h = XXH.h32( input, seed ).toString(16)
		var h64 = XXH.h64( input, seed ).toString(16)
		document.getElementById('input').innerHTML = "input ='" + input + "'"
		document.getElementById('seed').innerHTML = "seed='" + seed + "'"
		document.getElementById('xxhash').innerHTML = "xxHash='" + h + "'"
		document.getElementById('xxhash64').innerHTML = "xxHash64='" + h64 + "'"
	</script>
</body>
</html>
