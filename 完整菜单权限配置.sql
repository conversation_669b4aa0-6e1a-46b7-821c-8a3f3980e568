-- 完整的菜单权限配置
-- 为admin管理员用户配置所有管理权限

USE blog;

-- 1. 首先添加缺失的菜单权限项
-- 文章管理权限
INSERT IGNORE INTO `sys_menu` VALUES (2029, '文章查询', 2019, 1, '', NULL, 1, 'F', '0', '0', 'content:article:query', '#', NULL, NOW(), NULL, NOW(), '', '0');
INSERT IGNORE INTO `sys_menu` VALUES (2030, '文章新增', 2019, 2, '', NULL, 1, 'F', '0', '0', 'content:article:writer', '#', NULL, NOW(), NULL, NOW(), '', '0');
INSERT IGNORE INTO `sys_menu` VALUES (2031, '文章修改', 2019, 3, '', NULL, 1, 'F', '0', '0', 'content:article:edit', '#', NULL, NOW(), NULL, NOW(), '', '0');
INSERT IGNORE INTO `sys_menu` VALUES (2032, '文章删除', 2019, 4, '', NULL, 1, 'F', '0', '0', 'content:article:remove', '#', NULL, NOW(), NULL, NOW(), '', '0');

-- 分类管理权限
INSERT IGNORE INTO `sys_menu` VALUES (2033, '分类查询', 2018, 1, '', NULL, 1, 'F', '0', '0', 'content:category:query', '#', NULL, NOW(), NULL, NOW(), '', '0');
INSERT IGNORE INTO `sys_menu` VALUES (2034, '分类新增', 2018, 2, '', NULL, 1, 'F', '0', '0', 'content:category:add', '#', NULL, NOW(), NULL, NOW(), '', '0');
INSERT IGNORE INTO `sys_menu` VALUES (2035, '分类修改', 2018, 3, '', NULL, 1, 'F', '0', '0', 'content:category:edit', '#', NULL, NOW(), NULL, NOW(), '', '0');
INSERT IGNORE INTO `sys_menu` VALUES (2036, '分类删除', 2018, 4, '', NULL, 1, 'F', '0', '0', 'content:category:remove', '#', NULL, NOW(), NULL, NOW(), '', '0');

-- 标签管理权限
INSERT IGNORE INTO `sys_menu` VALUES (2037, '标签查询', 2021, 1, '', NULL, 1, 'F', '0', '0', 'content:tag:query', '#', NULL, NOW(), NULL, NOW(), '', '0');
INSERT IGNORE INTO `sys_menu` VALUES (2038, '标签新增', 2021, 2, '', NULL, 1, 'F', '0', '0', 'content:tag:add', '#', NULL, NOW(), NULL, NOW(), '', '0');
INSERT IGNORE INTO `sys_menu` VALUES (2039, '标签修改', 2021, 3, '', NULL, 1, 'F', '0', '0', 'content:tag:edit', '#', NULL, NOW(), NULL, NOW(), '', '0');
INSERT IGNORE INTO `sys_menu` VALUES (2040, '标签删除', 2021, 4, '', NULL, 1, 'F', '0', '0', 'content:tag:remove', '#', NULL, NOW(), NULL, NOW(), '', '0');

-- 2. 查询admin用户的角色ID
-- 假设admin用户对应的角色是ID为1的"超级管理员"角色

-- 3. 为管理员角色添加所有菜单权限
-- 清除现有的管理员角色菜单关联（除了特殊权限标识0）
DELETE FROM `sys_role_menu` WHERE role_id = 1 AND menu_id != 0;

-- 为管理员角色添加所有菜单权限
INSERT INTO `sys_role_menu` (role_id, menu_id) VALUES 
-- 系统管理模块
(1, 1),    -- 系统管理目录
(1, 100),  -- 用户管理
(1, 101),  -- 角色管理  
(1, 102),  -- 菜单管理
-- 用户管理权限
(1, 1001), (1, 1002), (1, 1003), (1, 1004), (1, 1005), (1, 1006), (1, 1007),
-- 角色管理权限
(1, 1008), (1, 1009), (1, 1010), (1, 1011), (1, 1012),
-- 菜单管理权限
(1, 1013), (1, 1014), (1, 1015), (1, 1016),
-- 内容管理模块
(1, 2017), -- 内容管理目录
(1, 2018), -- 分类管理
(1, 2019), -- 文章管理
(1, 2021), -- 标签管理
(1, 2022), -- 友链管理
(1, 2023), -- 写博文
-- 文章管理权限
(1, 2029), (1, 2030), (1, 2031), (1, 2032),
-- 分类管理权限
(1, 2033), (1, 2034), (1, 2035), (1, 2036),
-- 标签管理权限
(1, 2037), (1, 2038), (1, 2039), (1, 2040),
-- 友链管理权限
(1, 2024), (1, 2025), (1, 2026), (1, 2027),
-- 其他权限
(1, 2028); -- 导出分类

-- 4. 确保admin用户关联到管理员角色
-- 查看当前用户角色关联
SELECT u.user_name, r.role_name, r.role_key 
FROM sys_user u 
LEFT JOIN sys_user_role ur ON u.id = ur.user_id 
LEFT JOIN sys_role r ON ur.role_id = r.id 
WHERE u.user_name = 'admin';

-- 如果admin用户没有关联到管理员角色，执行以下语句：
-- 首先删除admin用户的现有角色关联
-- DELETE FROM sys_user_role WHERE user_id = (SELECT id FROM sys_user WHERE user_name = 'admin');
-- 然后添加admin用户到管理员角色
-- INSERT INTO sys_user_role (user_id, role_id) VALUES 
-- ((SELECT id FROM sys_user WHERE user_name = 'admin'), 1);

-- 5. 验证配置
SELECT '=== 菜单权限配置完成 ===' as status;
SELECT COUNT(*) as total_menus FROM sys_menu;
SELECT COUNT(*) as admin_role_menus FROM sys_role_menu WHERE role_id = 1;
