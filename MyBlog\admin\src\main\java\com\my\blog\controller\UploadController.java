package com.my.blog.controller;

import com.my.blog.domain.ResponseResult;
import com.my.blog.service.UploadService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

@RestController
public class UploadController {
    @Resource
    private UploadService uploadService;

    @PostMapping("/upload")
    @ResponseBody
    public ResponseResult upload(@RequestParam("img") MultipartFile img) {
        return uploadService.upload(img);
    }
}

