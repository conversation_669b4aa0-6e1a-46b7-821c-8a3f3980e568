package com.my.blog.domain.dto;

import com.my.blog.domain.entity.Role;

import java.util.List;

/**
 * 添加角色DTO
 */
public class AddRoleDto {
    
    private Role role;
    private List<Long> menuIds;

    public AddRoleDto() {
    }

    public AddRoleDto(Role role, List<Long> menuIds) {
        this.role = role;
        this.menuIds = menuIds;
    }

    public Role getRole() {
        return role;
    }

    public void setRole(Role role) {
        this.role = role;
    }

    public List<Long> getMenuIds() {
        return menuIds;
    }

    public void setMenuIds(List<Long> menuIds) {
        this.menuIds = menuIds;
    }

    // 为了兼容前端直接传递角色属性，添加角色的getter和setter方法
    public Long getId() {
        return role != null ? role.getId() : null;
    }

    public void setId(Long id) {
        if (role == null) {
            role = new Role();
        }
        role.setId(id);
    }

    public String getRoleName() {
        return role != null ? role.getRoleName() : null;
    }

    public void setRoleName(String roleName) {
        if (role == null) {
            role = new Role();
        }
        role.setRoleName(roleName);
    }

    public String getRoleKey() {
        return role != null ? role.getRoleKey() : null;
    }

    public void setRoleKey(String roleKey) {
        if (role == null) {
            role = new Role();
        }
        role.setRoleKey(roleKey);
    }

    public Integer getRoleSort() {
        return role != null ? role.getRoleSort() : null;
    }

    public void setRoleSort(Integer roleSort) {
        if (role == null) {
            role = new Role();
        }
        role.setRoleSort(roleSort);
    }

    public String getStatus() {
        return role != null ? role.getStatus() : null;
    }

    public void setStatus(String status) {
        if (role == null) {
            role = new Role();
        }
        role.setStatus(status);
    }

    public String getRemark() {
        return role != null ? role.getRemark() : null;
    }

    public void setRemark(String remark) {
        if (role == null) {
            role = new Role();
        }
        role.setRemark(remark);
    }
}
