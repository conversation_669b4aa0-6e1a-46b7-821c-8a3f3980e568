package com.my.blog.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.my.blog.domain.ResponseResult;
import com.my.blog.domain.entity.User;

/**
 * <p>
 * 用户表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-19
 */
public interface IUserService extends IService<User> {

    ResponseResult userInfo();

    ResponseResult updateUser(User user);

    ResponseResult register(User user);

    // 系统管理员用户管理功能
    ResponseResult getUserList(Integer pageNum, Integer pageSize, String userName, String phonenumber, String status);

    ResponseResult addUser(User user, List<Long> roleIds);

    ResponseResult deleteUser(Long id);

    ResponseResult getUserById(Long id);

    ResponseResult updateSystemUser(User user, List<Long> roleIds);
}
