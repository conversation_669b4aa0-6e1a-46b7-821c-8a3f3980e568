package com.my.blog.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.my.blog.dao.UserMapper;
import com.my.blog.domain.ResponseResult;
import com.my.blog.domain.entity.User;
import com.my.blog.domain.vo.PageVo;
import com.my.blog.domain.vo.UserInfoVo;
import com.my.blog.enums.AppHttpCodeEnum;
import com.my.blog.exception.SystemException;
import com.my.blog.service.IUserRoleService;
import com.my.blog.service.IUserService;
import com.my.blog.utils.BeanCopyUtils;
import com.my.blog.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 用户表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-19
 */
@Service
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements IUserService {

    @Resource
    private UserMapper userMapper;

    @Resource
    private PasswordEncoder passwordEncoder;

    @Autowired
    private IUserRoleService userRoleService;

    @Override
    public ResponseResult userInfo() {
        Long userId = SecurityUtils.getUserId();
        User user =userMapper.selectById(userId);
        return ResponseResult.okResult(BeanCopyUtils.copyBean(user, UserInfoVo.class));
    }

    @Override
    public ResponseResult updateUser(User user) {
        userMapper.updateById(user);
        return ResponseResult.okResult();
    }

    @Override
    public ResponseResult register(User user) {
//        合法性校验
        if (user.getUserName().isEmpty()){
            throw new SystemException(AppHttpCodeEnum.USERNAME_NOT_NULL);
        }
        if (user.getNickName().isEmpty()){
            throw new SystemException(AppHttpCodeEnum.NICKNAME_NOT_NULL);
        }
        if (user.getEmail().isEmpty()){
            throw new SystemException(AppHttpCodeEnum.EMAIL_NOT_NULL);
        }
//        重复性校验
        if(isUserNameExist(user.getUserName())){
            throw new SystemException(AppHttpCodeEnum.USERNAME_EXIST);
        }
        if(isEmailExist(user.getEmail())){
            throw new SystemException(AppHttpCodeEnum.EMAIL_EXIST);
        }
        if(isNickNameExist(user.getNickName())){
            throw new SystemException(AppHttpCodeEnum.NICKNAME_EXIST);
        }
//        密文
    user.setPassword(passwordEncoder.encode(user.getPassword()));
//        保存

        userMapper.insert(user);
        return ResponseResult.okResult();
    }

    private boolean isNickNameExist(String nickName) {
        return userMapper.selectCount(new LambdaQueryWrapper<User>().eq(User::getNickName,nickName)) > 0;

    }

    private boolean isUserNameExist(String userName){
       return userMapper.selectCount(new LambdaQueryWrapper<User>().eq(User::getUserName,userName)) > 0;
    }

    @Override
    public ResponseResult getUserList(Integer pageNum, Integer pageSize, String userName, String phonenumber, String status) {
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();

        // 构建查询条件
        queryWrapper.like(StringUtils.hasText(userName), User::getUserName, userName)
                   .like(StringUtils.hasText(phonenumber), User::getPhonenumber, phonenumber)
                   .eq(StringUtils.hasText(status), User::getStatus, status)
                   .eq(User::getDelFlag, 0); // 只查询未删除的用户

        // 分页查询
        Page<User> page = new Page<>(pageNum, pageSize);
        page(page, queryWrapper);

        // 转换为PageVo
        PageVo pageVo = new PageVo(page.getRecords(), page.getTotal());
        return ResponseResult.okResult(pageVo);
    }

    @Override
    public ResponseResult addUser(User user, List<Long> roleIds) {
        // 参数校验
        if (!StringUtils.hasText(user.getUserName())) {
            throw new SystemException(AppHttpCodeEnum.REQUIRE_USERNAME);
        }

        // 检查用户名是否已存在
        if (isUserNameExist(user.getUserName())) {
            throw new SystemException(AppHttpCodeEnum.USERNAME_EXIST);
        }

        // 检查手机号是否已存在
        if (StringUtils.hasText(user.getPhonenumber()) && isPhonenumberExist(user.getPhonenumber())) {
            return ResponseResult.errorResult(500, "手机号已存在");
        }

        // 检查邮箱是否已存在
        if (StringUtils.hasText(user.getEmail()) && isEmailExist(user.getEmail())) {
            return ResponseResult.errorResult(500, "邮箱已存在");
        }

        // 密码加密
        user.setPassword(passwordEncoder.encode(user.getPassword()));

        // 设置创建者
        user.setCreateBy(SecurityUtils.getUserId());

        // 保存用户
        save(user);

        // 保存用户角色关联
        if (roleIds != null && !roleIds.isEmpty()) {
            userRoleService.saveUserRoles(user.getId(), roleIds);
        }

        return ResponseResult.okResult();
    }

    @Override
    public ResponseResult deleteUser(Long id) {
        // 不能删除当前操作的用户
        Long currentUserId = SecurityUtils.getUserId();
        if (currentUserId.equals(id)) {
            return ResponseResult.errorResult(500, "不能删除当前操作的用户");
        }

        // 逻辑删除
        User user = new User();
        user.setId(id);
        user.setDelFlag(1);
        updateById(user);

        return ResponseResult.okResult();
    }

    @Override
    public ResponseResult getUserById(Long id) {
        User user = getById(id);
        if (user == null) {
            return ResponseResult.errorResult(500, "用户不存在");
        }
        return ResponseResult.okResult(user);
    }

    @Override
    public ResponseResult updateSystemUser(User user, List<Long> roleIds) {
        // 设置更新者
        user.setUpdateBy(SecurityUtils.getUserId());

        // 更新用户信息
        updateById(user);

        // 更新用户角色关联
        userRoleService.saveUserRoles(user.getId(), roleIds);

        return ResponseResult.okResult();
    }

    private boolean isPhonenumberExist(String phonenumber) {
        return userMapper.selectCount(new LambdaQueryWrapper<User>()
                .eq(User::getPhonenumber, phonenumber)
                .eq(User::getDelFlag, 0)) > 0;
    }

    private boolean isEmailExist(String email) {
        return userMapper.selectCount(new LambdaQueryWrapper<User>()
                .eq(User::getEmail, email)
                .eq(User::getDelFlag, 0)) > 0;
    }
}
