package com.my.blog.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.my.blog.dao.UserMapper;
import com.my.blog.domain.ResponseResult;
import com.my.blog.domain.entity.User;
import com.my.blog.domain.vo.UserInfoVo;
import com.my.blog.enums.AppHttpCodeEnum;
import com.my.blog.exception.SystemException;
import com.my.blog.service.IUserService;
import com.my.blog.utils.BeanCopyUtils;
import com.my.blog.utils.SecurityUtils;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>
 * 用户表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-19
 */
@Service
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements IUserService {

    @Resource
    private UserMapper userMapper;

    @Resource
    private PasswordEncoder passwordEncoder;

    @Override
    public ResponseResult userInfo() {
        Long userId = SecurityUtils.getUserId();
        User user =userMapper.selectById(userId);
        return ResponseResult.okResult(BeanCopyUtils.copyBean(user, UserInfoVo.class));
    }

    @Override
    public ResponseResult updateUser(User user) {
        userMapper.updateById(user);
        return ResponseResult.okResult();
    }

    @Override
    public ResponseResult register(User user) {
//        合法性校验
        if (user.getUserName().isEmpty()){
            throw new SystemException(AppHttpCodeEnum.USERNAME_NOT_NULL);
        }
        if (user.getNickName().isEmpty()){
            throw new SystemException(AppHttpCodeEnum.NICKNAME_NOT_NULL);
        }
        if (user.getEmail().isEmpty()){
            throw new SystemException(AppHttpCodeEnum.EMAIL_NOT_NULL);
        }
//        重复性校验
        if(isUserNameExist(user.getUserName())){
            throw new SystemException(AppHttpCodeEnum.USERNAME_EXIST);
        }
        if(isEmailExist(user.getEmail())){
            throw new SystemException(AppHttpCodeEnum.EMAIL_EXIST);
        }
        if(isNickNameExist(user.getNickName())){
            throw new SystemException(AppHttpCodeEnum.NICKNAME_EXIST);
        }
//        密文
    user.setPassword(passwordEncoder.encode(user.getPassword()));
//        保存

        userMapper.insert(user);
        return ResponseResult.okResult();
    }

    private boolean isNickNameExist(String nickName) {
        return userMapper.selectCount(new LambdaQueryWrapper<User>().eq(User::getNickName,nickName)) > 0;

    }

    private boolean isEmailExist(String email) {
        return userMapper.selectCount(new LambdaQueryWrapper<User>().eq(User::getEmail,email)) > 0;

    }

    private boolean isUserNameExist(String userName){
       return userMapper.selectCount(new LambdaQueryWrapper<User>().eq(User::getUserName,userName)) > 0;
    }
}
