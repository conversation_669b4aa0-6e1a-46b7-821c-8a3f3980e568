package com.my.blog.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.my.blog.dao.UserRoleMapper;
import com.my.blog.domain.entity.UserRole;
import com.my.blog.service.IUserRoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 用户角色关联表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-22
 */
@Service
public class UserRoleServiceImpl extends ServiceImpl<UserRoleMapper, UserRole> implements IUserRoleService {

    @Autowired
    private UserRoleMapper userRoleMapper;

    @Override
    public List<Long> selectRoleIdsByUserId(Long userId) {
        return userRoleMapper.selectRoleIdsByUserId(userId);
    }

    @Override
    @Transactional
    public void saveUserRoles(Long userId, List<Long> roleIds) {
        // 先删除用户的所有角色关联
        deleteByUserId(userId);
        
        // 如果有新的角色，则插入
        if (roleIds != null && !roleIds.isEmpty()) {
            List<UserRole> userRoles = roleIds.stream()
                    .map(roleId -> new UserRole(userId, roleId))
                    .collect(Collectors.toList());
            saveBatch(userRoles);
        }
    }

    @Override
    public void deleteByUserId(Long userId) {
        userRoleMapper.deleteByUserId(userId);
    }
}
