{"name": "window-size", "description": "Reliable way to to get the height and width of the terminal/console in a node.js environment.", "version": "0.1.0", "homepage": "https://github.com/jonschlinkert/window-size", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "https://github.com/jonschlinkert/window-size.git"}, "bugs": {"url": "https://github.com/jonschlinkert/window-size/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/jonschlinkert/window-size/blob/master/LICENSE-MIT"}], "main": "index.js", "engines": {"node": ">= 0.8.0"}, "keywords": ["window", "console", "terminal", "tty"]}