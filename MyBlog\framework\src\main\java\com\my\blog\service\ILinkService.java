package com.my.blog.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.my.blog.domain.ResponseResult;
import com.my.blog.domain.entity.Link;

/**
 * <p>
 * 友链 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-19
 */
public interface ILinkService extends IService<Link> {

    ResponseResult getAllLink();

    // 友链管理功能
    ResponseResult getAdminLinkList(Integer pageNum, Integer pageSize, String name, String status);

    ResponseResult addLink(Link link);

    ResponseResult updateLink(Link link);

    ResponseResult deleteLink(Long id);

    ResponseResult getLinkById(Long id);

    ResponseResult changeLinkStatus(Long linkId, String status);
}
