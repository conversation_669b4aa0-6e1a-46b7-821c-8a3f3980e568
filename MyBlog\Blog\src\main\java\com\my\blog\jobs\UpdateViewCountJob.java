package com.my.blog.jobs;

import com.my.blog.constant.SystemConstants;
import com.my.blog.domain.entity.Article;
import com.my.blog.service.IArticleService;
import com.my.blog.utils.RedisCache;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class UpdateViewCountJob {
    @Resource
    private RedisCache redisCache;
    @Autowired
    private IArticleService articleService;

    @Scheduled(cron = "0/20 * * * * *")
    public void updateViewCount() {
        Map<String, Object> map = redisCache.getCacheMap(SystemConstants.VIEW_CONT);
      List<Article> collect = map.entrySet().stream()
                .map(entry -> new Article(Long.valueOf(entry.getKey().toString()), Long.valueOf(entry.getValue().toString())))
                .collect(Collectors.toList());
        articleService.updateBatchById(collect);
    }
}
