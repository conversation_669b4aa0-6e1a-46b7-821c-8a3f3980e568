{"name": "ws", "version": "5.2.3", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "homepage": "https://github.com/websockets/ws", "bugs": "https://github.com/websockets/ws/issues", "repository": "websockets/ws", "author": "<PERSON><PERSON> <PERSON> <<EMAIL>> (http://2x.io)", "license": "MIT", "main": "index.js", "files": ["index.js", "lib"], "scripts": {"test": "eslint . && nyc --reporter=html --reporter=text mocha test/*.test.js", "integration": "eslint . && mocha test/*.integration.js", "lint": "eslint ."}, "dependencies": {"async-limiter": "~1.0.0"}, "devDependencies": {"benchmark": "~2.1.2", "bufferutil": "~3.0.0", "eslint": "~4.19.0", "eslint-config-standard": "~11.0.0", "eslint-plugin-import": "~2.12.0", "eslint-plugin-node": "~6.0.0", "eslint-plugin-promise": "~3.8.0", "eslint-plugin-standard": "~3.0.0", "mocha": "~5.2.0", "nyc": "~12.0.2", "utf-8-validate": "~4.0.0"}}