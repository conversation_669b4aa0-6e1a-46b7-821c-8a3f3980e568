package com.my.blog.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.Query;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.my.blog.constant.SystemConstants;
import com.my.blog.dao.LinkMapper;
import com.my.blog.domain.ResponseResult;
import com.my.blog.domain.entity.Link;
import com.my.blog.domain.vo.LinkVo;
import com.my.blog.service.ILinkService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 友链 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-19
 */
@Service
public class LinkServiceImpl extends ServiceImpl<LinkMapper, Link> implements ILinkService {
    @Autowired
    private LinkMapper linkMapper;


    @Override
    public ResponseResult getAllLink() {
//        只展示审核通过的友情链接
        LambdaQueryWrapper<Link> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Link::getStatus, SystemConstants.Link_STATUS_NORMAL );


        List<Link> links = linkMapper.selectList(queryWrapper);


//        封装vo
        ArrayList<LinkVo> linkVos = new ArrayList<>();
        for(Link link: links){
            LinkVo linkVo = new LinkVo();
            BeanUtils.copyProperties(link , linkVo);
            linkVos.add(linkVo);
        }
        return ResponseResult.okResult(linkVos);
    }
}
