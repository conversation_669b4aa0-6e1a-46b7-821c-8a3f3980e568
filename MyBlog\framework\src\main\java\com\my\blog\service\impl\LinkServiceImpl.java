package com.my.blog.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.my.blog.constant.SystemConstants;
import com.my.blog.dao.LinkMapper;
import com.my.blog.domain.ResponseResult;
import com.my.blog.domain.entity.Link;
import com.my.blog.domain.vo.LinkVo;
import com.my.blog.domain.vo.PageVo;
import com.my.blog.service.ILinkService;
import com.my.blog.utils.SecurityUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 友链 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-19
 */
@Service
public class LinkServiceImpl extends ServiceImpl<LinkMapper, Link> implements ILinkService {
    @Autowired
    private LinkMapper linkMapper;


    @Override
    public ResponseResult getAllLink() {
//        只展示审核通过的友情链接
        LambdaQueryWrapper<Link> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Link::getStatus, SystemConstants.Link_STATUS_NORMAL );


        List<Link> links = linkMapper.selectList(queryWrapper);


//        封装vo
        ArrayList<LinkVo> linkVos = new ArrayList<>();
        for(Link link: links){
            LinkVo linkVo = new LinkVo();
            BeanUtils.copyProperties(link , linkVo);
            linkVos.add(linkVo);
        }
        return ResponseResult.okResult(linkVos);
    }

    @Override
    public ResponseResult getAdminLinkList(Integer pageNum, Integer pageSize, String name, String status) {
        LambdaQueryWrapper<Link> queryWrapper = new LambdaQueryWrapper<>();

        // 构建查询条件
        queryWrapper.like(StringUtils.hasText(name), Link::getName, name)
                   .eq(StringUtils.hasText(status), Link::getStatus, status)
                   .eq(Link::getDelFlag, 0); // 只查询未删除的友链

        // 分页查询
        Page<Link> page = new Page<>(pageNum, pageSize);
        page(page, queryWrapper);

        // 转换为PageVo
        PageVo pageVo = new PageVo(page.getRecords(), page.getTotal());
        return ResponseResult.okResult(pageVo);
    }

    @Override
    public ResponseResult addLink(Link link) {
        // 检查友链名称是否已存在
        if (isLinkNameExist(link.getName())) {
            return ResponseResult.errorResult(500, "友链名称已存在");
        }

        // 设置创建者
        link.setCreateBy(SecurityUtils.getUserId());
        link.setDelFlag(0);
        link.setStatus("2"); // 默认未审核状态

        // 保存友链
        save(link);

        return ResponseResult.okResult();
    }

    @Override
    public ResponseResult updateLink(Link link) {
        // 设置更新者
        link.setUpdateBy(SecurityUtils.getUserId());

        // 更新友链信息
        updateById(link);

        return ResponseResult.okResult();
    }

    @Override
    public ResponseResult deleteLink(Long id) {
        // 逻辑删除
        Link link = new Link();
        link.setId(id);
        link.setDelFlag(1);
        link.setUpdateBy(SecurityUtils.getUserId());
        updateById(link);

        return ResponseResult.okResult();
    }

    @Override
    public ResponseResult getLinkById(Long id) {
        Link link = getById(id);
        if (link == null || link.getDelFlag() == 1) {
            return ResponseResult.errorResult(500, "友链不存在");
        }
        return ResponseResult.okResult(link);
    }

    @Override
    public ResponseResult changeLinkStatus(Long linkId, String status) {
        Link link = new Link();
        link.setId(linkId);
        link.setStatus(status);
        link.setUpdateBy(SecurityUtils.getUserId());
        updateById(link);

        return ResponseResult.okResult();
    }

    private boolean isLinkNameExist(String name) {
        return count(new LambdaQueryWrapper<Link>()
                .eq(Link::getName, name)
                .eq(Link::getDelFlag, 0)) > 0;
    }
}
