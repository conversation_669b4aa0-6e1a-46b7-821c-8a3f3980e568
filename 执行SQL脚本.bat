@echo off
echo 正在配置博客管理系统菜单权限...
echo.

REM 尝试不同的MySQL连接方式
echo 请输入MySQL root用户的密码：
set /p password=

echo.
echo 正在执行SQL脚本...
mysql -u root -p%password% blog < "完整菜单权限配置.sql"

if %errorlevel% equ 0 (
    echo.
    echo ✅ 菜单权限配置成功！
    echo.
    echo 请按以下步骤操作：
    echo 1. 重启后端服务（Spring Boot应用）
    echo 2. 清除浏览器缓存
    echo 3. 重新登录admin账号
    echo 4. 现在应该能看到完整的管理菜单了
) else (
    echo.
    echo ❌ 配置失败，请检查：
    echo 1. MySQL服务是否正在运行
    echo 2. 数据库密码是否正确
    echo 3. blog数据库是否存在
)

echo.
pause
