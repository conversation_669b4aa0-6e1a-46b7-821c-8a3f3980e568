package com.my.blog.controller;

import com.my.blog.domain.ResponseResult;
import com.my.blog.domain.entity.Category;
import com.my.blog.domain.vo.CategoryVo;
import com.my.blog.service.ICategoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/content/category")
public class CategoryController {
    @Autowired
    private ICategoryService categoryService;

    /**
     * 查询所有分类（用于写文章时选择分类）
     */
    @GetMapping("/listAllCategory")
    public ResponseResult listAllCategory() {
        List<CategoryVo> list = categoryService.listAllCategory();
        return ResponseResult.okResult(list);
    }

    /**
     * 分类列表（分页查询）
     */
    @GetMapping("/list")
    public ResponseResult getCategoryList(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String name,
            @RequestParam(required = false) String status) {
        return categoryService.getAdminCategoryList(pageNum, pageSize, name, status);
    }

    /**
     * 新增分类
     */
    @PostMapping
    public ResponseResult addCategory(@RequestBody Category category) {
        return categoryService.addCategory(category);
    }

    /**
     * 修改分类
     */
    @PutMapping
    public ResponseResult updateCategory(@RequestBody Category category) {
        return categoryService.updateCategory(category);
    }

    /**
     * 删除分类
     */
    @DeleteMapping("/{id}")
    public ResponseResult deleteCategory(@PathVariable Long id) {
        return categoryService.deleteCategory(id);
    }

    /**
     * 根据id查询分类信息
     */
    @GetMapping("/{id}")
    public ResponseResult getCategoryById(@PathVariable Long id) {
        return categoryService.getCategoryById(id);
    }

}
