package com.my.blog.controller;

import com.my.blog.domain.ResponseResult;
import com.my.blog.domain.entity.Menu;
import com.my.blog.service.IMenuService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 系统菜单管理控制器
 */
@RestController
@RequestMapping("/system/menu")
public class SystemMenuController {

    @Autowired
    private IMenuService menuService;

    /**
     * 菜单列表
     */
    @GetMapping("/list")
    public ResponseResult getMenuList(
            @RequestParam(required = false) String menuName,
            @RequestParam(required = false) String status) {
        return menuService.getMenuList(menuName, status);
    }

    /**
     * 新增菜单
     */
    @PostMapping
    public ResponseResult addMenu(@RequestBody Menu menu) {
        return menuService.addMenu(menu);
    }

    /**
     * 修改菜单
     */
    @PutMapping
    public ResponseResult updateMenu(@RequestBody Menu menu) {
        return menuService.updateMenu(menu);
    }

    /**
     * 删除菜单
     */
    @DeleteMapping("/{id}")
    public ResponseResult deleteMenu(@PathVariable Long id) {
        return menuService.deleteMenu(id);
    }

    /**
     * 根据id查询菜单信息
     */
    @GetMapping("/{id}")
    public ResponseResult getMenuById(@PathVariable Long id) {
        return menuService.getMenuById(id);
    }

    /**
     * 获取菜单树形结构
     */
    @GetMapping("/treeselect")
    public ResponseResult getMenuTree() {
        return menuService.getMenuTree();
    }

    /**
     * 根据角色ID查询菜单下拉树结构
     */
    @GetMapping("/roleMenuTreeselect/{roleId}")
    public ResponseResult getRoleMenuTreeselect(@PathVariable Long roleId) {
        return menuService.getRoleMenuTreeselect(roleId);
    }
}
