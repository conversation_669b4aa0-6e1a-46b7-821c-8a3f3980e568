package com.my.blog.domain.dto;

import com.my.blog.domain.entity.User;

import java.util.List;

/**
 * 新增用户DTO
 */
public class AddUserDto {
    
    private User user;
    
    private List<Long> roleIds;

    public AddUserDto() {
    }

    public AddUserDto(User user, List<Long> roleIds) {
        this.user = user;
        this.roleIds = roleIds;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public List<Long> getRoleIds() {
        return roleIds;
    }

    public void setRoleIds(List<Long> roleIds) {
        this.roleIds = roleIds;
    }

    @Override
    public String toString() {
        return "AddUserDto{" +
                "user=" + user +
                ", roleIds=" + roleIds +
                '}';
    }
}
