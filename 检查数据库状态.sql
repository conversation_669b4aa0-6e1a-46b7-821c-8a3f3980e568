-- 检查数据库当前状态
USE blog;

-- 1. 检查用户表
SELECT '=== 用户信息 ===' as info;
SELECT id, user_name, nick_name, status, del_flag FROM sys_user;

-- 2. 检查角色表
SELECT '=== 角色信息 ===' as info;
SELECT id, role_name, role_key, status, del_flag FROM sys_role;

-- 3. 检查用户角色关联
SELECT '=== 用户角色关联 ===' as info;
SELECT u.user_name, r.role_name, r.role_key 
FROM sys_user u 
LEFT JOIN sys_user_role ur ON u.id = ur.user_id 
LEFT JOIN sys_role r ON ur.role_id = r.id;

-- 4. 检查菜单表
SELECT '=== 菜单信息 ===' as info;
SELECT id, menu_name, parent_id, order_num, path, component, menu_type, visible, status 
FROM sys_menu 
ORDER BY parent_id, order_num;

-- 5. 检查角色菜单关联
SELECT '=== 角色菜单关联 ===' as info;
SELECT rm.role_id, r.role_name, rm.menu_id, m.menu_name 
FROM sys_role_menu rm 
LEFT JOIN sys_role r ON rm.role_id = r.id 
LEFT JOIN sys_menu m ON rm.menu_id = m.id 
ORDER BY rm.role_id, rm.menu_id;
