package com.my.blog.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.my.blog.dao.RoleMapper;
import com.my.blog.domain.ResponseResult;
import com.my.blog.domain.entity.Role;
import com.my.blog.domain.vo.PageVo;
import com.my.blog.service.IRoleMenuService;
import com.my.blog.service.IRoleService;
import com.my.blog.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 角色信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-19
 */
@Service
public class RoleServiceImpl extends ServiceImpl<RoleMapper, Role> implements IRoleService {

    @Autowired
    private RoleMapper roleMapper;

    @Autowired
    private IRoleMenuService roleMenuService;

    @Override
    public List<String> selectRoleKeyByUserId(Long id) {
        if (id == 1L) {
            ArrayList<String> roles = new ArrayList<>();
            roles.add("admin");
            return roles;
        }
        List<String> roles = roleMapper.selectRoleKeyByUserId(id);
        return roles;
    }

    @Override
    public ResponseResult getRoleList(Integer pageNum, Integer pageSize, String roleName, String status) {
        LambdaQueryWrapper<Role> queryWrapper = new LambdaQueryWrapper<>();

        // 构建查询条件
        queryWrapper.like(StringUtils.hasText(roleName), Role::getRoleName, roleName)
                   .eq(StringUtils.hasText(status), Role::getStatus, status)
                   .eq(Role::getDelFlag, "0") // 只查询未删除的角色
                   .orderByAsc(Role::getRoleSort);

        // 分页查询
        Page<Role> page = new Page<>(pageNum, pageSize);
        page(page, queryWrapper);

        // 转换为PageVo
        PageVo pageVo = new PageVo(page.getRecords(), page.getTotal());
        return ResponseResult.okResult(pageVo);
    }

    @Override
    public ResponseResult addRole(Role role) {
        // 检查角色名是否已存在
        if (isRoleNameExist(role.getRoleName())) {
            return ResponseResult.errorResult(500, "角色名已存在");
        }

        // 检查角色权限字符串是否已存在
        if (isRoleKeyExist(role.getRoleKey())) {
            return ResponseResult.errorResult(500, "角色权限字符串已存在");
        }

        // 设置创建者
        role.setCreateBy(SecurityUtils.getUserId());
        role.setDelFlag("0");
        role.setStatus("0"); // 默认正常状态

        // 保存角色
        save(role);

        return ResponseResult.okResult();
    }

    @Override
    public ResponseResult addRoleWithMenus(Role role, List<Long> menuIds) {
        // 检查角色名是否已存在
        if (isRoleNameExist(role.getRoleName())) {
            return ResponseResult.errorResult(500, "角色名已存在");
        }

        // 检查角色权限字符串是否已存在
        if (isRoleKeyExist(role.getRoleKey())) {
            return ResponseResult.errorResult(500, "角色权限字符串已存在");
        }

        // 设置创建者
        role.setCreateBy(SecurityUtils.getUserId());
        role.setDelFlag("0");
        role.setStatus("0"); // 默认正常状态

        // 保存角色
        save(role);

        // 保存角色菜单关联
        if (menuIds != null && !menuIds.isEmpty()) {
            roleMenuService.saveRoleMenus(role.getId(), menuIds);
        }

        return ResponseResult.okResult();
    }

    @Override
    public ResponseResult updateRole(Role role) {
        // 设置更新者
        role.setUpdateBy(SecurityUtils.getUserId());

        // 更新角色信息
        updateById(role);

        return ResponseResult.okResult();
    }

    @Override
    public ResponseResult updateRoleWithMenus(Role role, List<Long> menuIds) {
        // 设置更新者
        role.setUpdateBy(SecurityUtils.getUserId());

        // 更新角色信息
        updateById(role);

        // 更新角色菜单关联
        roleMenuService.saveRoleMenus(role.getId(), menuIds);

        return ResponseResult.okResult();
    }

    @Override
    public ResponseResult deleteRole(Long id) {
        // 逻辑删除
        Role role = new Role();
        role.setId(id);
        role.setDelFlag("1");
        role.setUpdateBy(SecurityUtils.getUserId());
        updateById(role);

        return ResponseResult.okResult();
    }

    @Override
    public ResponseResult getRoleById(Long id) {
        Role role = getById(id);
        if (role == null || "1".equals(role.getDelFlag())) {
            return ResponseResult.errorResult(500, "角色不存在");
        }
        return ResponseResult.okResult(role);
    }

    @Override
    public ResponseResult changeStatus(Long roleId, String status) {
        Role role = new Role();
        role.setId(roleId);
        role.setStatus(status);
        role.setUpdateBy(SecurityUtils.getUserId());
        updateById(role);

        return ResponseResult.okResult();
    }

    private boolean isRoleNameExist(String roleName) {
        return count(new LambdaQueryWrapper<Role>()
                .eq(Role::getRoleName, roleName)
                .eq(Role::getDelFlag, "0")) > 0;
    }

    private boolean isRoleKeyExist(String roleKey) {
        return count(new LambdaQueryWrapper<Role>()
                .eq(Role::getRoleKey, roleKey)
                .eq(Role::getDelFlag, "0")) > 0;
    }
}
