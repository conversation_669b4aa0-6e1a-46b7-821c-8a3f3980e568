package com.my.blog.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.my.blog.dao.RoleMapper;
import com.my.blog.service.IRoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.my.blog.domain.entity.Role;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 角色信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-19
 */
@Service
public class RoleServiceImpl extends ServiceImpl<RoleMapper, Role> implements IRoleService {

    @Autowired
    private RoleMapper roleMapper;

    @Override
    public List<String> selectRoleKeyByUserId(Long id) {
        if (id == 1L) {
            ArrayList<String> roles = new ArrayList<>();
            roles.add("admin");
            return roles;
        }
        List<String> roles = roleMapper.selectRoleKeyByUserId(id);
        return roles;
    }
}
