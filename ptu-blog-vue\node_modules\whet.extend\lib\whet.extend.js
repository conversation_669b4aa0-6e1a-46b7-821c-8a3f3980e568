// Generated by CoffeeScript 1.3.3

/*
 * whet.extend v0.9.7
 * Standalone port of jQuery.extend that actually works on node.js
 * https://github.com/Meettya/whet.extend
 *
 * Copyright 2012, <PERSON><PERSON><PERSON><PERSON>
 * Released under the MIT License
*/


(function() {
  var extend, _findValue, _isClass, _isOwnProp, _isPlainObj, _isPrimitiveType, _isTypeOf, _prepareClone,
    __slice = [].slice;

  module.exports = extend = function() {
    var args, copy, deep, name, options, target, _i, _len, _ref;
    deep = arguments[0], target = arguments[1], args = 3 <= arguments.length ? __slice.call(arguments, 2) : [];
    if (!_isClass(deep, 'Boolean')) {
      args.unshift(target);
      _ref = [deep || {}, false], target = _ref[0], deep = _ref[1];
    }
    if (_isPrimitiveType(target)) {
      target = {};
    }
    for (_i = 0, _len = args.length; _i < _len; _i++) {
      options = args[_i];
      if (options != null) {
        for (name in options) {
          copy = options[name];
          target[name] = _findValue(deep, copy, target[name]);
        }
      }
    }
    return target;
  };

  /*
  Internal methods from now
  */


  _isClass = function(obj, str) {
    return ("[object " + str + "]") === Object.prototype.toString.call(obj);
  };

  _isOwnProp = function(obj, prop) {
    return Object.prototype.hasOwnProperty.call(obj, prop);
  };

  _isTypeOf = function(obj, str) {
    return str === typeof obj;
  };

  _isPlainObj = function(obj) {
    var key;
    if (!obj) {
      return false;
    }
    if (obj.nodeType || obj.setInterval || !_isClass(obj, 'Object')) {
      return false;
    }
    if (obj.constructor && !_isOwnProp(obj, 'constructor') && !_isOwnProp(obj.constructor.prototype, 'isPrototypeOf')) {
      return false;
    }
    for (key in obj) {
      key;

    }
    return key === void 0 || _isOwnProp(obj, key);
  };

  _isPrimitiveType = function(obj) {
    return !(_isTypeOf(obj, 'object') || _isTypeOf(obj, 'function'));
  };

  _prepareClone = function(copy, src) {
    if (_isClass(copy, 'Array')) {
      if (_isClass(src, 'Array')) {
        return src;
      } else {
        return [];
      }
    } else {
      if (_isPlainObj(src)) {
        return src;
      } else {
        return {};
      }
    }
  };

  _findValue = function(deep, copy, src) {
    var clone;
    if (deep && (_isClass(copy, 'Array') || _isPlainObj(copy))) {
      clone = _prepareClone(copy, src);
      return extend(deep, clone, copy);
    } else {
      return copy;
    }
  };

}).call(this);
