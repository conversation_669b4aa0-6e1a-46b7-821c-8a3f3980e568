package com.my.blog.exception.handler;

import com.alibaba.fastjson.JSON;
import com.my.blog.domain.ResponseResult;
import com.my.blog.enums.AppHttpCodeEnum;
import com.my.blog.utils.WebUtils;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.InsufficientAuthenticationException;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.stereotype.Component;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
//AuthenticationEntryPoint 认证失败处理器
@Component
public class AuthenticationEntryPointImpl implements AuthenticationEntryPoint {
 @Override
 public void commence(HttpServletRequest request, HttpServletResponse response,
                      AuthenticationException authException) throws IOException, ServletException {
 authException.printStackTrace();
 //InsufficientAuthenticationException
 //BadCredentialsException
 ResponseResult result = null;
 if(authException instanceof BadCredentialsException){
 result =
ResponseResult.errorResult(AppHttpCodeEnum.LOGIN_ERROR.getCode(),authException.getMessage());
 }else if(authException instanceof InsufficientAuthenticationException){
 result = ResponseResult.errorResult(AppHttpCodeEnum.NEED_LOGIN);
 }else{
 result =
ResponseResult.errorResult(AppHttpCodeEnum.SYSTEM_ERROR.getCode(),"认证或授权失败");
 }
 //响应给前端
 WebUtils.renderString(response, JSON.toJSONString(result));
 }
}