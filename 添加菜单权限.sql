-- 添加缺失的菜单权限
-- 注意：执行前请确保数据库中没有重复的菜单ID

-- 添加文章管理的权限按钮
INSERT INTO `sys_menu` VALUES (2029, '文章查询', 2019, 1, '', NULL, 1, 'F', '0', '0', 'content:article:query', '#', NULL, NOW(), NULL, NOW(), '', '0');
INSERT INTO `sys_menu` VALUES (2030, '文章新增', 2019, 2, '', NULL, 1, 'F', '0', '0', 'content:article:writer', '#', NULL, NOW(), NULL, NOW(), '', '0');
INSERT INTO `sys_menu` VALUES (2031, '文章修改', 2019, 3, '', NULL, 1, 'F', '0', '0', 'content:article:edit', '#', NULL, NOW(), NULL, NOW(), '', '0');
INSERT INTO `sys_menu` VALUES (2032, '文章删除', 2019, 4, '', NULL, 1, 'F', '0', '0', 'content:article:remove', '#', NULL, NOW(), NULL, NOW(), '', '0');

-- 添加分类管理的权限按钮
INSERT INTO `sys_menu` VALUES (2033, '分类查询', 2018, 1, '', NULL, 1, 'F', '0', '0', 'content:category:query', '#', NULL, NOW(), NULL, NOW(), '', '0');
INSERT INTO `sys_menu` VALUES (2034, '分类新增', 2018, 2, '', NULL, 1, 'F', '0', '0', 'content:category:add', '#', NULL, NOW(), NULL, NOW(), '', '0');
INSERT INTO `sys_menu` VALUES (2035, '分类修改', 2018, 3, '', NULL, 1, 'F', '0', '0', 'content:category:edit', '#', NULL, NOW(), NULL, NOW(), '', '0');
INSERT INTO `sys_menu` VALUES (2036, '分类删除', 2018, 4, '', NULL, 1, 'F', '0', '0', 'content:category:remove', '#', NULL, NOW(), NULL, NOW(), '', '0');

-- 添加标签管理的权限按钮
INSERT INTO `sys_menu` VALUES (2037, '标签查询', 2021, 1, '', NULL, 1, 'F', '0', '0', 'content:tag:query', '#', NULL, NOW(), NULL, NOW(), '', '0');
INSERT INTO `sys_menu` VALUES (2038, '标签新增', 2021, 2, '', NULL, 1, 'F', '0', '0', 'content:tag:add', '#', NULL, NOW(), NULL, NOW(), '', '0');
INSERT INTO `sys_menu` VALUES (2039, '标签修改', 2021, 3, '', NULL, 1, 'F', '0', '0', 'content:tag:edit', '#', NULL, NOW(), NULL, NOW(), '', '0');
INSERT INTO `sys_menu` VALUES (2040, '标签删除', 2021, 4, '', NULL, 1, 'F', '0', '0', 'content:tag:remove', '#', NULL, NOW(), NULL, NOW(), '', '0');

-- 为管理员角色添加所有权限
-- 首先查询管理员角色ID（通常是1）
-- 然后为管理员角色分配所有新增的权限

-- 注意：管理员角色（ID=1）在数据库中有特殊权限标识（menu_id=0），表示拥有所有权限
-- 所以不需要为管理员角色单独添加每个菜单权限
-- 管理员会自动拥有所有菜单的访问权限

-- 如果需要为其他角色分配权限，可以使用以下语句：
-- 例如为角色ID=2的普通角色添加部分权限：
-- INSERT INTO `sys_role_menu` (role_id, menu_id) VALUES
-- (2, 2029), (2, 2030), (2, 2031), (2, 2032);  -- 文章管理权限
