-- 最终修复脚本 - 解决admin用户菜单显示问题
USE blog;

-- 1. 检查当前admin用户的ID和角色关联
SELECT '=== 检查admin用户信息 ===' as step;
SELECT u.id as user_id, u.user_name, ur.role_id, r.role_name 
FROM sys_user u 
LEFT JOIN sys_user_role ur ON u.id = ur.user_id 
LEFT JOIN sys_role r ON ur.role_id = r.id 
WHERE u.user_name = 'admin';

-- 2. 检查管理员角色的菜单权限
SELECT '=== 检查管理员角色菜单权限 ===' as step;
SELECT rm.role_id, rm.menu_id, m.menu_name, m.menu_type, m.status 
FROM sys_role_menu rm 
LEFT JOIN sys_menu m ON rm.menu_id = m.id 
WHERE rm.role_id = 1 
ORDER BY rm.menu_id;

-- 3. 确保admin用户关联到管理员角色（role_id = 1）
DELETE FROM sys_user_role WHERE user_id = (SELECT id FROM sys_user WHERE user_name = 'admin');
INSERT INTO sys_user_role (user_id, role_id) 
VALUES ((SELECT id FROM sys_user WHERE user_name = 'admin'), 1);

-- 4. 为管理员角色添加完整的菜单权限
-- 先清除现有权限（保留特殊权限0）
DELETE FROM sys_role_menu WHERE role_id = 1 AND menu_id != 0;

-- 添加所有菜单权限
INSERT INTO sys_role_menu (role_id, menu_id) 
SELECT 1, id FROM sys_menu WHERE status = '0' AND del_flag = '0';

-- 5. 验证配置结果
SELECT '=== 验证admin用户角色关联 ===' as step;
SELECT u.user_name, r.role_name, r.role_key 
FROM sys_user u 
JOIN sys_user_role ur ON u.id = ur.user_id 
JOIN sys_role r ON ur.role_id = r.id 
WHERE u.user_name = 'admin';

SELECT '=== 验证管理员角色菜单数量 ===' as step;
SELECT COUNT(*) as menu_count FROM sys_role_menu WHERE role_id = 1;

SELECT '=== 验证系统管理菜单 ===' as step;
SELECT id, menu_name, menu_type, status, del_flag 
FROM sys_menu 
WHERE menu_name = '系统管理' OR parent_id = 1;

SELECT '配置完成！' as result;
