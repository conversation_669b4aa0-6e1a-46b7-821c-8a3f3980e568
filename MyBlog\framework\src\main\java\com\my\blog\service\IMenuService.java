package com.my.blog.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.my.blog.domain.entity.Menu;
import com.my.blog.domain.vo.MenuVo;

import java.util.List;


/**
 * <p>
 * 菜单权限表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-19
 */
public interface IMenuService extends IService<Menu> {

    List<String> selectPermsByUserId(Long id);

    List<MenuVo> selectRouterMenuTreeByUserId(Long userId);
}
