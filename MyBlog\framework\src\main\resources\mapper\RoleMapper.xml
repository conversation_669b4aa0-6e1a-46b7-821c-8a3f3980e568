<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.my.blog.dao.RoleMapper">

    <select id="selectRoleKeyByUserId" resultType="java.lang.String">
        select sr.role_key
        from sys_user_role sur
                 left join sys_role sr on sur.role_id = sr.id
        where sur.user_id = #{id}
          and sr.status = 0
          and sr.del_flag = 0
    </select>
</mapper>