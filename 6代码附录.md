# 6代码附录

## 项目整体架构

本博客系统采用前后端分离的架构设计，包含以下三个主要部分：

- **前台前端(ptu-blog-vue)**: 基于Vue.js 2.5.2 + Element UI的博客展示系统
- **后台前端(ptu-admin-vue)**: 基于Vue.js + Element UI的管理后台系统
- **后端服务(MyBlog)**: 基于SpringBoot + MyBatis-Plus + Spring Security的多模块后端服务

### 技术栈总览

| 模块 | 技术栈 | 端口 | 主要功能 |
|------|--------|------|----------|
| 前台前端 | Vue.js 2.5.2 + Element UI + Axios | 8080 | 博客展示、文章浏览、评论互动 |
| 后台前端 | Vue.js + Element UI + Mavon Editor | 9528 | 内容管理、用户管理、权限配置 |
| 前台后端 | SpringBoot + MyBatis-Plus | 7777 | 博客API服务 |
| 后台后端 | SpringBoot + Spring Security | 8989 | 管理API服务 |

## 6.1前台前端功能

### 6.1.1前台前端实现方法

前台前端采用Vue.js 2.5.2框架开发，使用Element UI组件库构建用户界面。项目采用Webpack进行模块打包，支持热重载开发模式。

使用Vue Router进行路由管理，实现单页面应用(SPA)的页面跳转和导航。

使用Vuex进行状态管理，统一管理用户登录状态、文章数据等全局状态。

使用Axios进行HTTP请求，与后端API进行数据交互，支持请求拦截和响应拦截。

集成Mavon Editor富文本编辑器，支持Markdown格式的文章编写和预览。

### 6.1.2前台前端主要页面

| 页面名称 | 路由路径 | 功能描述 | 主要组件 |
|---------|----------|----------|----------|
| 首页 | / | 博客首页展示 | Home.vue |
| 文章列表 | /article | 文章列表页面 | ArticleList.vue |
| 文章详情 | /article/:id | 文章详情页面 | ArticleDetail.vue |
| 分类页面 | /category | 文章分类页面 | Category.vue |
| 标签页面 | /tag | 文章标签页面 | Tag.vue |
| 友链页面 | /link | 友情链接页面 | Link.vue |
| 关于页面 | /about | 关于我页面 | About.vue |

### 6.1.3前台前端技术配置

```javascript
// package.json核心依赖
{
  "name": "ptu-vue-blog",
  "version": "1.0.0",
  "description": "PTU博客",
  "dependencies": {
    "axios": "^0.17.0",
    "element-ui": "^1.4.12",
    "js-cookie": "2.2.0",
    "mavon-editor": "^2.10.4",
    "vue": "^2.5.2",
    "vue-router": "^3.0.1",
    "vuex": "^3.0.1"
  }
}
```

### 6.1.4前台前端核心代码

#### 主入口文件

```javascript
// main.js
import Vue from 'vue'
import App from './App'
import router from './router'
import store from './store'
import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
import mavonEditor from 'mavon-editor'
import 'mavon-editor/dist/css/index.css'

Vue.use(ElementUI)
Vue.use(mavonEditor)

new Vue({
  el: '#app',
  router,
  store,
  components: { App },
  template: '<App/>'
})
```

#### 路由配置

```javascript
// router/index.js
import Vue from 'vue'
import Router from 'vue-router'
import Home from '@/views/Home'
import Article from '@/views/Article'
import ArticleDetail from '@/views/ArticleDetail'

Vue.use(Router)

export default new Router({
  mode: 'history',
  routes: [
    {
      path: '/',
      name: 'Home',
      component: Home
    },
    {
      path: '/article',
      name: 'Article',
      component: Article
    },
    {
      path: '/article/:id',
      name: 'ArticleDetail',
      component: ArticleDetail
    }
  ]
})
```

## 6.2后台前端功能

### 6.2.1后台前端实现方法

后台前端基于vue-admin-template模板开发，采用Vue.js + Element UI技术栈构建现代化的管理后台界面。

使用Vue CLI进行项目构建和开发环境配置，支持热重载、代码分割等现代化开发特性。

集成权限控制系统，通过路由守卫和动态菜单实现基于角色的访问控制。

使用Vuex进行状态管理，统一管理用户信息、权限数据、菜单路由等全局状态。

集成Mavon Editor富文本编辑器，支持文章的可视化编辑和Markdown预览。

### 6.2.2后台前端接口设计

| 功能模块 | API路径 | 请求方式 | 功能描述 |
|---------|---------|----------|----------|
| 用户登录 | /user/login | POST | 管理员登录认证 |
| 获取用户信息 | /getInfo | GET | 获取当前用户信息 |
| 获取路由菜单 | /getRouters | GET | 获取用户权限菜单 |
| 文章管理 | /content/article/* | GET/POST/PUT/DELETE | 文章CRUD操作 |
| 分类管理 | /content/category/* | GET/POST/PUT/DELETE | 分类CRUD操作 |
| 标签管理 | /content/tag/* | GET/POST/PUT/DELETE | 标签CRUD操作 |
| 用户管理 | /system/user/* | GET/POST/PUT/DELETE | 用户CRUD操作 |
| 角色管理 | /system/role/* | GET/POST/PUT/DELETE | 角色CRUD操作 |
| 菜单管理 | /system/menu/* | GET/POST/PUT/DELETE | 菜单CRUD操作 |

### 6.2.3后台前端响应格式

```json
{
    "code": 200,
    "data": {
        "rows": [
            {
                "id": "1",
                "title": "SpringBoot入门教程",
                "summary": "详细介绍SpringBoot的基础知识",
                "categoryId": "1",
                "thumbnail": "http://example.com/image.jpg",
                "isTop": "0",
                "status": "0",
                "viewCount": "100",
                "isComment": "1",
                "createTime": "2025-06-22 10:00:00"
            }
        ],
        "total": "1"
    },
    "msg": "操作成功"
}
```

### 6.2.4后台前端核心代码

#### 主配置文件

```javascript
// main.js
import Vue from 'vue'
import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
import App from './App'
import store from './store'
import router from './router'
import '@/permission' // permission control
import mavonEditor from 'mavon-editor'
import 'mavon-editor/dist/css/index.css'

Vue.use(ElementUI)
Vue.use(mavonEditor)

new Vue({
  el: '#app',
  router,
  store,
  render: h => h(App)
})
```

#### 权限控制

```javascript
// permission.js
import router from './router'
import store from './store'
import { getToken } from '@/utils/auth'

const whiteList = ['/login'] // 白名单

router.beforeEach(async(to, from, next) => {
  const hasToken = getToken()

  if (hasToken) {
    if (to.path === '/login') {
      next({ path: '/' })
    } else {
      const hasRoles = store.getters.roles && store.getters.roles.length > 0
      if (hasRoles) {
        next()
      } else {
        try {
          const { roles } = await store.dispatch('user/getInfo')
          const accessRoutes = await store.dispatch('permission/generateRoutes', roles)
          router.addRoutes(accessRoutes)
          next({ ...to, replace: true })
        } catch (error) {
          await store.dispatch('user/resetToken')
          next(`/login?redirect=${to.path}`)
        }
      }
    }
  } else {
    if (whiteList.indexOf(to.path) !== -1) {
      next()
    } else {
      next(`/login?redirect=${to.path}`)
    }
  }
})
```

#### API接口封装

```javascript
// api/content/article.js
import request from '@/utils/request'

// 查询文章列表
export function listArticle(query) {
  return request({
    url: '/content/article/list',
    method: 'get',
    params: query
  })
}

// 新增文章
export function addArticle(data) {
  return request({
    url: '/content/article',
    method: 'post',
    data: data
  })
}

// 修改文章
export function updateArticle(data) {
  return request({
    url: '/content/article',
    method: 'put',
    data: data
  })
}

// 删除文章
export function delArticle(id) {
  return request({
    url: '/content/article/' + id,
    method: 'delete'
  })
}

// 查询文章详细
export function getArticle(id) {
  return request({
    url: '/content/article/' + id,
    method: 'get'
  })
}
```

#### Vuex状态管理

```javascript
// store/modules/user.js
import { login, logout, getInfo } from '@/api/login'
import { getToken, setToken, removeToken } from '@/utils/auth'

const state = {
  token: getToken(),
  name: '',
  avatar: '',
  roles: [],
  permissions: []
}

const mutations = {
  SET_TOKEN: (state, token) => {
    state.token = token
  },
  SET_NAME: (state, name) => {
    state.name = name
  },
  SET_AVATAR: (state, avatar) => {
    state.avatar = avatar
  },
  SET_ROLES: (state, roles) => {
    state.roles = roles
  },
  SET_PERMISSIONS: (state, permissions) => {
    state.permissions = permissions
  }
}

const actions = {
  // 登录
  login({ commit }, userInfo) {
    const username = userInfo.username.trim()
    const password = userInfo.password
    const code = userInfo.code
    const uuid = userInfo.uuid
    return new Promise((resolve, reject) => {
      login(username, password, code, uuid).then(response => {
        const { data } = response
        commit('SET_TOKEN', data.token)
        setToken(data.token)
        resolve()
      }).catch(error => {
        reject(error)
      })
    })
  },

  // 获取用户信息
  getInfo({ commit, state }) {
    return new Promise((resolve, reject) => {
      getInfo().then(response => {
        const { data } = response
        const { roles, name, avatar, permissions } = data

        commit('SET_ROLES', roles)
        commit('SET_NAME', name)
        commit('SET_AVATAR', avatar)
        commit('SET_PERMISSIONS', permissions)
        resolve(data)
      }).catch(error => {
        reject(error)
      })
    })
  },

  // 退出系统
  logout({ commit, state, dispatch }) {
    return new Promise((resolve, reject) => {
      logout(state.token).then(() => {
        commit('SET_TOKEN', '')
        commit('SET_ROLES', [])
        commit('SET_PERMISSIONS', [])
        removeToken()
        dispatch('tagsView/delAllViews', null, { root: true })
        resolve()
      }).catch(error => {
        reject(error)
      })
    })
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
```

## 6.3角色列表功能

### 6.3.1角色列表实现方法

使用@RestController注解的控制器类来处理HTTP请求，用于处理角色相关的JSON响应。

使用@RequestMapping("/system/role")指定了该控制器的基础路径，所有该控制器的请求都会以"/system/role"为前缀。

@Autowired - 角色管理依赖注入了角色管理的API接口。

当用户Service层接口实现类是一个聚合器，用于处理角色管理的业务逻辑。

该接口Service实现类继承了MyBatis-Plus的ServiceImpl，实现了IRoleService接口，并指定了RoleMapper、Role>，实现了角色管理的数据访问。

getRoleList(IRoleService)方法收集角色信息，并根据条件查询角色列表，然后返回分页的角色信息。

利用LambdaQueryWrapper构造器，动态拼接查询条件，然后调用page()方法进行分页查询。

将查询结果封装为PageVo对象返回。

### 6.3.2角色列表接口设计

| 请求方式 | 请求路径 | 是否需要token头 |
|---------|----------|----------------|
| GET | system/role/list | 是 |

### 6.3.3响应格式

```json
{
    "code": 200,
    "data": {
        "rows": [
            {
                "id": "2",
                "roleName": "友链审核员",
                "roleKey": "link",
                "roleSort": "1",
                "status": "0"
            }
        ],
        "total": "1"
    },
    "msg": "操作成功"
}
```

### 6.3.4代码实现

#### RoleController

```java
@RestController
@RequestMapping("/system/role")
public class RoleController {
    
    @Autowired
    private IRoleService roleService;
    
    @GetMapping("/list")
    public ResponseResult getRoleList(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String roleName,
            @RequestParam(required = false) String status) {
        return roleService.getRoleList(pageNum, pageSize, roleName, status);
    }
    
    @PostMapping
    public ResponseResult addRole(@RequestBody Role role) {
        return roleService.addRole(role);
    }
    
    @PutMapping
    public ResponseResult updateRole(@RequestBody Role role) {
        return roleService.updateRole(role);
    }
    
    @DeleteMapping("/{id}")
    public ResponseResult deleteRole(@PathVariable Long id) {
        return roleService.deleteRole(id);
    }
    
    @GetMapping("/{id}")
    public ResponseResult getRoleById(@PathVariable Long id) {
        return roleService.getRoleById(id);
    }
    
    @PutMapping("/changeStatus")
    public ResponseResult changeStatus(@RequestBody Role role) {
        return roleService.changeStatus(role);
    }
}
```

#### IRoleService接口

```java
public interface IRoleService extends IService<Role> {
    
    List<String> selectRoleKeyByUserId(Long id);
    
    // 角色管理功能
    ResponseResult getRoleList(Integer pageNum, Integer pageSize, String roleName, String status);
    
    ResponseResult addRole(Role role);
    
    ResponseResult updateRole(Role role);
    
    ResponseResult deleteRole(Long id);
    
    ResponseResult getRoleById(Long id);
    
    ResponseResult changeStatus(Role role);
    
    ResponseResult getAllRoles();
}
```

#### RoleServiceImpl实现类

```java
@Service
public class RoleServiceImpl extends ServiceImpl<RoleMapper, Role> implements IRoleService {

    @Autowired
    private RoleMapper roleMapper;

    @Override
    public List<String> selectRoleKeyByUserId(Long id) {
        if (id == 1L) {
            ArrayList<String> roles = new ArrayList<>();
            roles.add("admin");
            return roles;
        }
        List<String> roles = roleMapper.selectRoleKeyByUserId(id);
        return roles;
    }

    @Override
    public ResponseResult getRoleList(Integer pageNum, Integer pageSize, String roleName, String status) {
        LambdaQueryWrapper<Role> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StringUtils.hasText(roleName), Role::getRoleName, roleName)
                   .eq(StringUtils.hasText(status), Role::getStatus, status)
                   .eq(Role::getDelFlag, "0")
                   .orderByAsc(Role::getRoleSort);

        Page<Role> page = new Page<>(pageNum, pageSize);
        page(page, queryWrapper);

        PageVo pageVo = new PageVo(page.getRecords(), page.getTotal());
        return ResponseResult.okResult(pageVo);
    }

    @Override
    public ResponseResult addRole(Role role) {
        // 检查角色名是否已存在
        if (isRoleNameExist(role.getRoleName())) {
            return ResponseResult.errorResult(500, "角色名已存在");
        }
        
        // 检查角色权限字符是否已存在
        if (isRoleKeyExist(role.getRoleKey())) {
            return ResponseResult.errorResult(500, "角色权限字符已存在");
        }

        // 设置创建者
        role.setCreateBy(SecurityUtils.getUserId());
        role.setDelFlag("0");

        // 保存角色
        save(role);

        return ResponseResult.okResult();
    }

    @Override
    public ResponseResult updateRole(Role role) {
        // 设置更新者
        role.setUpdateBy(SecurityUtils.getUserId());

        // 更新角色信息
        updateById(role);

        return ResponseResult.okResult();
    }

    @Override
    public ResponseResult deleteRole(Long id) {
        // 检查是否有用户关联该角色
        if (hasUsersWithRole(id)) {
            return ResponseResult.errorResult(500, "该角色下还有用户，不能删除");
        }

        // 逻辑删除
        Role role = new Role();
        role.setId(id);
        role.setDelFlag("1");
        role.setUpdateBy(SecurityUtils.getUserId());
        updateById(role);

        return ResponseResult.okResult();
    }

    @Override
    public ResponseResult getRoleById(Long id) {
        Role role = getById(id);
        if (role == null || "1".equals(role.getDelFlag())) {
            return ResponseResult.errorResult(500, "角色不存在");
        }
        return ResponseResult.okResult(role);
    }

    @Override
    public ResponseResult changeStatus(Role role) {
        // 设置更新者
        role.setUpdateBy(SecurityUtils.getUserId());
        
        // 只更新状态
        Role updateRole = new Role();
        updateRole.setId(role.getId());
        updateRole.setStatus(role.getStatus());
        updateRole.setUpdateBy(role.getUpdateBy());
        updateById(updateRole);

        return ResponseResult.okResult();
    }

    @Override
    public ResponseResult getAllRoles() {
        LambdaQueryWrapper<Role> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Role::getStatus, "0")
                   .eq(Role::getDelFlag, "0")
                   .select(Role::getId, Role::getRoleName);
        
        List<Role> roles = list(queryWrapper);
        return ResponseResult.okResult(roles);
    }

    private boolean isRoleNameExist(String roleName) {
        return count(new LambdaQueryWrapper<Role>()
                .eq(Role::getRoleName, roleName)
                .eq(Role::getDelFlag, "0")) > 0;
    }

    private boolean isRoleKeyExist(String roleKey) {
        return count(new LambdaQueryWrapper<Role>()
                .eq(Role::getRoleKey, roleKey)
                .eq(Role::getDelFlag, "0")) > 0;
    }

    private boolean hasUsersWithRole(Long roleId) {
        // 这里需要查询用户角色关联表，检查是否有用户关联该角色
        // 具体实现需要根据实际的用户角色关联表来编写
        return false;
    }
}
```

#### Role实体类

```java
@TableName("sys_role")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class Role {
    @TableId
    private Long id;

    private String roleName;
    private String roleKey;
    private Integer roleSort;
    private String status;
    private String delFlag;
    private Long createBy;
    private Date createTime;
    private Long updateBy;
    private Date updateTime;
    private String remark;
}
```

## 6.4用户管理功能

### 6.4.1用户管理实现方法

用户管理模块采用RESTful API设计，通过SystemUserController处理用户相关的HTTP请求。该控制器使用@RestController注解，支持JSON格式的数据交互。

使用@RequestMapping("/system/user")指定控制器的基础路径，所有用户管理相关的请求都以"/system/user"为前缀。

业务逻辑层通过IUserService接口定义服务方法，UserServiceImpl类实现具体的业务逻辑，包括用户的增删改查、角色分配、状态管理等功能。

数据访问层使用MyBatis-Plus框架，通过UserMapper接口实现数据库操作，支持动态SQL构建和分页查询。

### 6.4.2用户管理接口设计

| 请求方式 | 请求路径 | 功能描述 | 是否需要token头 |
|---------|----------|----------|----------------|
| GET | /system/user/list | 查询用户列表 | 是 |
| POST | /system/user | 新增用户 | 是 |
| PUT | /system/user | 修改用户 | 是 |
| DELETE | /system/user/{id} | 删除用户 | 是 |
| GET | /system/user/{id} | 查询用户详情 | 是 |
| PUT | /system/user/changeStatus | 修改用户状态 | 是 |

### 6.4.3响应格式

```json
{
    "code": 200,
    "data": {
        "rows": [
            {
                "id": "1",
                "userName": "admin",
                "nickName": "管理员",
                "email": "<EMAIL>",
                "phonenumber": "13800138000",
                "sex": "0",
                "status": "0",
                "createTime": "2025-06-22 10:00:00"
            }
        ],
        "total": "1"
    },
    "msg": "操作成功"
}
```

### 6.4.4代码实现

#### SystemUserController

```java
@RestController
@RequestMapping("/system/user")
public class SystemUserController {

    @Resource
    private IUserService userService;

    @GetMapping("/list")
    public ResponseResult getUserList(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String userName,
            @RequestParam(required = false) String phonenumber,
            @RequestParam(required = false) String status) {
        return userService.getUserList(pageNum, pageSize, userName, phonenumber, status);
    }

    @PostMapping
    public ResponseResult addUser(@RequestBody AddUserDto addUserDto) {
        return userService.addUser(addUserDto.getUser(), addUserDto.getRoleIds());
    }

    @DeleteMapping("/{id}")
    public ResponseResult deleteUser(@PathVariable Long id) {
        return userService.deleteUser(id);
    }

    @GetMapping("/{id}")
    public ResponseResult getUserById(@PathVariable Long id) {
        return userService.getUserById(id);
    }

    @PutMapping
    public ResponseResult updateUser(@RequestBody AddUserDto addUserDto) {
        return userService.updateSystemUser(addUserDto.getUser(), addUserDto.getRoleIds());
    }

    @PutMapping("/changeStatus")
    public ResponseResult changeStatus(@RequestBody User user) {
        return userService.changeStatus(user);
    }
}
```

#### IUserService接口

```java
public interface IUserService extends IService<User> {
    ResponseResult userInfo();
    ResponseResult updateUserInfo(User user);
    ResponseResult register(User user);

    // 系统管理员用户管理功能
    ResponseResult getUserList(Integer pageNum, Integer pageSize,
                              String userName, String phonenumber, String status);
    ResponseResult addUser(User user, List<Long> roleIds);
    ResponseResult deleteUser(Long id);
    ResponseResult getUserById(Long id);
    ResponseResult updateSystemUser(User user, List<Long> roleIds);
    ResponseResult changeStatus(User user);
}
```

## 6.5文章管理功能

### 6.5.1文章管理实现方法

文章管理模块分为前台展示和后台管理两部分。前台ArticleController负责文章的展示功能，后台AdminArticleController负责文章的管理功能。

使用@PreAuthorize注解实现方法级别的权限控制，确保只有具有相应权限的用户才能执行特定操作。

文章与标签采用多对多关系设计，通过ArticleTag中间表维护关联关系。

支持文章的草稿和发布状态管理，提供文章置顶、评论开关等功能。

### 6.5.2文章管理接口设计

| 请求方式 | 请求路径 | 功能描述 | 是否需要token头 |
|---------|----------|----------|----------------|
| GET | /content/article/list | 查询文章列表 | 是 |
| POST | /content/article | 新增文章 | 是 |
| PUT | /content/article | 修改文章 | 是 |
| DELETE | /content/article/{id} | 删除文章 | 是 |
| GET | /content/article/{id} | 查询文章详情 | 是 |

### 6.5.3响应格式

```json
{
    "code": 200,
    "data": {
        "rows": [
            {
                "id": "1",
                "title": "SpringBoot入门教程",
                "summary": "详细介绍SpringBoot的基础知识",
                "categoryId": "1",
                "thumbnail": "http://example.com/image.jpg",
                "isTop": "0",
                "status": "0",
                "viewCount": "100",
                "isComment": "1",
                "createTime": "2025-06-22 10:00:00"
            }
        ],
        "total": "1"
    },
    "msg": "操作成功"
}
```

### 6.5.4代码实现

#### AdminArticleController

```java
@RestController
@RequestMapping("/content/article")
public class AdminArticleController {

    @Resource
    private IArticleService articleService;

    @PreAuthorize("@permissionService.hasPermission('content:article:list')")
    @GetMapping("/list")
    public ResponseResult getArticleList(Integer pageNum, Integer pageSize,
                                         String title, String summary) {
        ResponseResult articles = articleService.getAdminArticleList(pageNum,
                pageSize, title, summary);
        return articles;
    }

    @PreAuthorize("@permissionService.hasPermission('content:article:writer')")
    @PostMapping
    public ResponseResult addArticle(@RequestBody AddArticleDto addArticleDto) {
        return articleService.addArticle(addArticleDto);
    }

    @PreAuthorize("@permissionService.hasPermission('content:article:query')")
    @GetMapping("/{id}")
    public ResponseResult getArticleDetail(@PathVariable Long id) {
        return articleService.getAdminArticleDetail(id);
    }

    @PreAuthorize("@permissionService.hasPermission('content:article:edit')")
    @PutMapping
    public ResponseResult updateArticle(@RequestBody AddArticleDto addArticleDto) {
        return articleService.updateArticle(addArticleDto);
    }

    @PreAuthorize("@permissionService.hasPermission('content:article:remove')")
    @DeleteMapping("/{id}")
    public ResponseResult deleteArticle(@PathVariable Long id) {
        return articleService.deleteArticle(id);
    }
}
```

#### ArticleServiceImpl核心方法

```java
@Override
public ResponseResult addArticle(AddArticleDto addArticleDto) {
    // 拷贝属性
    Article article = BeanCopyUtils.copyBean(addArticleDto, Article.class);
    article.setCreateBy(SecurityUtils.getUserId());

    // 保存文章
    save(article);

    // 保存文章标签关联
    List<ArticleTag> articleTags = addArticleDto.getTags().stream()
            .map(tagId -> new ArticleTag(article.getId(), tagId))
            .collect(Collectors.toList());
    articleTagService.saveBatch(articleTags);

    return ResponseResult.okResult();
}

@Override
public ResponseResult getAdminArticleDetail(Long id) {
    // 查询文章基本信息
    Article article = getById(id);
    if (article == null) {
        return ResponseResult.errorResult(500, "文章不存在");
    }

    // 查询文章关联的标签ID
    LambdaQueryWrapper<ArticleTag> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(ArticleTag::getArticleId, id);
    List<ArticleTag> articleTags = articleTagService.list(queryWrapper);
    List<Long> tagIds = articleTags.stream().map(ArticleTag::getTagId).collect(Collectors.toList());

    // 封装返回数据
    AddArticleDto articleDto = BeanCopyUtils.copyBean(article, AddArticleDto.class);
    articleDto.setTags(tagIds);

    return ResponseResult.okResult(articleDto);
}

@Override
public ResponseResult updateArticle(AddArticleDto addArticleDto) {
    // 更新文章基本信息
    Article article = BeanCopyUtils.copyBean(addArticleDto, Article.class);
    article.setUpdateBy(SecurityUtils.getUserId());
    updateById(article);

    // 删除原有的标签关联
    LambdaQueryWrapper<ArticleTag> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(ArticleTag::getArticleId, article.getId());
    articleTagService.remove(queryWrapper);

    // 重新保存标签关联
    List<ArticleTag> articleTags = addArticleDto.getTags().stream()
            .map(tagId -> new ArticleTag(article.getId(), tagId))
            .collect(Collectors.toList());
    articleTagService.saveBatch(articleTags);

    return ResponseResult.okResult();
}
```

## 6.6菜单管理功能

### 6.6.1菜单管理实现方法

菜单管理模块负责系统菜单的层级结构管理和权限控制。采用树形结构设计，支持无限级菜单嵌套。

使用递归算法构建菜单树，通过parentId字段维护父子关系。

菜单类型分为目录(M)、菜单(C)、按钮(F)三种，支持不同级别的权限控制。

根据用户角色动态生成菜单树，实现个性化菜单展示。

### 6.6.2菜单管理接口设计

| 请求方式 | 请求路径 | 功能描述 | 是否需要token头 |
|---------|----------|----------|----------------|
| GET | /system/menu/list | 查询菜单列表 | 是 |
| POST | /system/menu | 新增菜单 | 是 |
| PUT | /system/menu | 修改菜单 | 是 |
| DELETE | /system/menu/{id} | 删除菜单 | 是 |
| GET | /system/menu/{id} | 查询菜单详情 | 是 |
| GET | /system/menu/treeselect | 获取菜单树 | 是 |

### 6.6.3响应格式

```json
{
    "code": 200,
    "data": [
        {
            "id": "1",
            "menuName": "系统管理",
            "parentId": "0",
            "orderNum": 1,
            "path": "system",
            "component": "",
            "menuType": "M",
            "visible": "0",
            "status": "0",
            "perms": "",
            "icon": "system",
            "children": [
                {
                    "id": "100",
                    "menuName": "用户管理",
                    "parentId": "1",
                    "orderNum": 1,
                    "path": "user",
                    "component": "system/user/index",
                    "menuType": "C",
                    "visible": "0",
                    "status": "0",
                    "perms": "system:user:list",
                    "icon": "user"
                }
            ]
        }
    ],
    "msg": "操作成功"
}
```

### 6.6.4代码实现

#### MenuServiceImpl核心方法

```java
@Override
public List<MenuVo> selectRouterMenuTreeByUserId(Long userId) {
    List<Menu> menus = null;
    // 判断是否是管理员
    if (userId == 1L) {
        LambdaQueryWrapper<Menu> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(Menu::getMenuType, "M", "C", "F")  // 包含目录(M)、菜单(C)、按钮(F)
                .eq(Menu::getStatus, SystemConstants.STATUS_NORMAL)
                .orderByAsc(Menu::getParentId, Menu::getOrderNum);
        menus = menuMapper.selectList(queryWrapper);
    } else {
        menus = menuMapper.selectRouterMenuByUserId(userId);
    }

    // 将perm值为null的替换为空字符串
    for (Menu menu : menus) {
        if (menu.getPerms() == null) {
            menu.setPerms("");
        }
    }

    // 做数据封装， 使用MenuVo
    List<MenuVo> menuVos = BeanCopyUtils.copyList(menus, MenuVo.class);

    // 构建父子菜单， 先找一级菜单， 再把子菜单设置到一级菜单的children中
    List<MenuVo> menuTree = buildMenuTree(menuVos);
    return menuTree;
}

private List<MenuVo> buildMenuTree(List<MenuVo> menuVos) {
    // 先找一级菜单
    List<MenuVo> menuTree = menuVos.stream().filter(menuVo -> menuVo.getParentId().equals(0L))
            // 把子菜单设置到一级菜单的children中
            .map(menuVo -> setChildrenList(menuVo, menuVos))
            .collect(Collectors.toList());
    return menuTree;
}

private MenuVo setChildrenList(MenuVo parent, List<MenuVo> menuVos) {
    // 从menuVos里找到所有parentId等于parent id的菜单， 就是parent的子菜单
    List<MenuVo> childrenList = menuVos.stream().filter(menuVo -> menuVo.getParentId().equals(parent.getId()))
            .collect(Collectors.toList());
    parent.setChildren(childrenList);
    return parent;
}
```

## 6.7友链管理功能

### 6.7.1友链管理实现方法

友链管理模块支持友情链接的申请、审核、展示等完整流程。采用状态机模式管理友链的生命周期。

友链状态包括：待审核(2)、审核通过(0)、审核拒绝(1)三种状态。

支持友链的批量操作和状态变更，提供完善的审核流程。

### 6.7.2友链管理接口设计

| 请求方式 | 请求路径 | 功能描述 | 是否需要token头 |
|---------|----------|----------|----------------|
| GET | /content/link/list | 查询友链列表 | 是 |
| POST | /content/link | 新增友链 | 是 |
| PUT | /content/link | 修改友链 | 是 |
| DELETE | /content/link/{id} | 删除友链 | 是 |
| GET | /content/link/{id} | 查询友链详情 | 是 |
| PUT | /content/link/changeStatus | 修改友链状态 | 是 |

### 6.7.3响应格式

```json
{
    "code": 200,
    "data": {
        "rows": [
            {
                "id": "1",
                "name": "技术博客",
                "logo": "http://example.com/logo.png",
                "description": "专注技术分享",
                "address": "http://example.com",
                "status": "0",
                "createTime": "2025-06-22 10:00:00"
            }
        ],
        "total": "1"
    },
    "msg": "操作成功"
}
```

### 6.7.4代码实现

#### SystemLinkController

```java
@RestController
@RequestMapping("/content/link")
public class SystemLinkController {

    @Autowired
    private ILinkService linkService;

    @GetMapping("/list")
    public ResponseResult getLinkList(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String name,
            @RequestParam(required = false) String status) {
        return linkService.getAdminLinkList(pageNum, pageSize, name, status);
    }

    @PostMapping
    public ResponseResult addLink(@RequestBody Link link) {
        return linkService.addLink(link);
    }

    @PutMapping
    public ResponseResult updateLink(@RequestBody Link link) {
        return linkService.updateLink(link);
    }

    @DeleteMapping("/{id}")
    public ResponseResult deleteLink(@PathVariable Long id) {
        return linkService.deleteLink(id);
    }

    @GetMapping("/{id}")
    public ResponseResult getLinkById(@PathVariable Long id) {
        return linkService.getLinkById(id);
    }

    @PutMapping("/changeStatus")
    public ResponseResult changeLinkStatus(@RequestBody Link link) {
        return linkService.changeLinkStatus(link.getId(), link.getStatus());
    }
}
```

## 6.8核心实体类

### 6.8.1用户实体类

```java
@TableName("sys_user")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class User implements Serializable {
    @TableId
    private Long id;

    /**
     * 用户名
     */
    @JsonProperty("username")
    private String userName;

    /**
     * 昵称
     */
    private String nickName;

    /**
     * 密码
     */
    private String password;

    /**
     * 用户类型：0代表普通用户，1代表管理员
     */
    private String type;

    /**
     * 账号状态（0正常 1停用）
     */
    private String status;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 手机号
     */
    private String phonenumber;

    /**
     * 用户性别（0男，1女，2未知）
     */
    private String sex;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 创建人的用户id
     */
    private Long createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人
     */
    private Long updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除标志（0代表未删除，1代表已删除）
     */
    private Integer delFlag;
}
```

### 6.8.2文章实体类

```java
@TableName("sg_article")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Article implements Serializable {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 标题
     */
    private String title;

    /**
     * 文章内容
     */
    private String content;

    /**
     * 文章摘要
     */
    private String summary;

    /**
     * 所属分类id
     */
    private Long categoryId;

    /**
     * 缩略图
     */
    private String thumbnail;

    /**
     * 是否置顶（0否，1是）
     */
    private String isTop;

    /**
     * 状态（0已发布，1草稿）
     */
    private String status;

    /**
     * 访问量
     */
    private Long viewCount;

    /**
     * 是否允许评论 1是，0否
     */
    private String isComment;

    private Long createBy;
    private Date createTime;
    private Long updateBy;
    private Date updateTime;
    private Integer delFlag;
}
```

### 6.8.3菜单实体类

```java
@TableName("sys_menu")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class Menu implements Serializable {

    /**
     * 菜单ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 菜单名称
     */
    private String menuName;

    /**
     * 父菜单ID
     */
    private Long parentId;

    /**
     * 显示顺序
     */
    private Integer orderNum;

    /**
     * 路由地址
     */
    private String path;

    /**
     * 组件路径
     */
    private String component;

    /**
     * 是否为外链（0是 1否）
     */
    private Integer isFrame;

    /**
     * 菜单类型（M目录 C菜单 F按钮）
     */
    private String menuType;

    /**
     * 菜单状态（0显示 1隐藏）
     */
    private String visible;

    /**
     * 菜单状态（0正常 1停用）
     */
    private String status;

    /**
     * 权限标识
     */
    private String perms;

    /**
     * 菜单图标
     */
    private String icon;

    /**
     * 创建者
     */
    private Long createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新者
     */
    private Long updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 备注
     */
    private String remark;
}
```

## 6.9工具类

### 6.9.1响应结果封装类

```java
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ResponseResult<T> {
    private Integer code;
    private String msg;
    private T data;

    public ResponseResult() {
        this.code = AppHttpCodeEnum.SUCCESS.getCode();
        this.msg = AppHttpCodeEnum.SUCCESS.getMsg();
    }

    public ResponseResult(Integer code, T data) {
        this.code = code;
        this.data = data;
    }

    public ResponseResult(Integer code, String msg, T data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
    }

    public ResponseResult(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public static ResponseResult errorResult(int code, String msg) {
        ResponseResult result = new ResponseResult();
        return result.error(code, msg);
    }

    public static ResponseResult okResult() {
        ResponseResult result = new ResponseResult();
        return result;
    }

    public static ResponseResult okResult(int code, String msg) {
        ResponseResult result = new ResponseResult();
        return result.ok(code, null, msg);
    }

    public static ResponseResult okResult(Object data) {
        ResponseResult result = setAppHttpCodeEnum(AppHttpCodeEnum.SUCCESS, AppHttpCodeEnum.SUCCESS.getMsg());
        if(data!=null) {
            result.setData(data);
        }
        return result;
    }

    public ResponseResult<?> error(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
        return this;
    }

    public ResponseResult<?> ok(Integer code, T data) {
        this.code = code;
        this.data = data;
        return this;
    }

    public ResponseResult<?> ok(Integer code, T data, String msg) {
        this.code = code;
        this.data = data;
        this.msg = msg;
        return this;
    }

    public ResponseResult<?> ok(T data) {
        this.data = data;
        return this;
    }

    public static ResponseResult setAppHttpCodeEnum(AppHttpCodeEnum enums){
        return okResult(enums.getCode(),enums.getMsg());
    }

    public static ResponseResult setAppHttpCodeEnum(AppHttpCodeEnum enums, String msg){
        return okResult(enums.getCode(),msg);
    }

    // getter和setter方法省略...
}
```

### 6.9.2Bean拷贝工具类

```java
public class BeanCopyUtils {

    private BeanCopyUtils() {
    }

    public static <V> V copyBean(Object source, Class<V> clazz) {
        V result = null;
        try {
            result = clazz.newInstance();
            BeanUtils.copyProperties(source, result);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    public static <O, V> List<V> copyList(List<O> list, Class<V> clazz) {
        return list.stream()
                .map(o -> copyBean(o, clazz))
                .collect(Collectors.toList());
    }
}
```

### 6.9.3安全工具类

```java
public class SecurityUtils {

    /**
     * 获取用户
     **/
    public static LoginUser getLoginUser() {
        return (LoginUser) getAuthentication().getPrincipal();
    }

    /**
     * 获取Authentication
     */
    public static Authentication getAuthentication() {
        return SecurityContextHolder.getContext().getAuthentication();
    }

    public static Boolean isAdmin() {
        Long id = getLoginUser().getUser().getId();
        return id != null && 1L == id;
    }

    public static Long getUserId() {
        return getLoginUser().getUser().getId();
    }
}
```

## 6.10配置类

### 6.10.1Spring Security配置

```java
@Configuration
@EnableGlobalMethodSecurity(prePostEnabled = true)
public class SecurityConfig {

    @Autowired
    private JwtAuthenticationTokenFilter jwtAuthenticationTokenFilter;

    @Autowired
    private AuthenticationEntryPoint authenticationEntryPoint;

    @Autowired
    private AccessDeniedHandler accessDeniedHandler;

    @Bean
    public PasswordEncoder passwordEncoder(){
        return new BCryptPasswordEncoder();
    }

    @Bean
    public AuthenticationManager authenticationManager(AuthenticationConfiguration config) throws Exception {
        return config.getAuthenticationManager();
    }

    @Bean
    SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        return http
                .csrf().disable()
                .sessionManagement().sessionCreationPolicy(SessionCreationPolicy.STATELESS)
                .and()
                .authorizeHttpRequests(authz -> authz
                        .requestMatchers("/login").anonymous()
                        .requestMatchers("/logout").authenticated()
                        .requestMatchers("/user/userInfo").authenticated()
                        .requestMatchers(HttpMethod.GET, "/link").permitAll()
                        .requestMatchers(HttpMethod.GET, "/article/**").permitAll()
                        .requestMatchers(HttpMethod.GET, "/category/**").permitAll()
                        .anyRequest().permitAll()
                )
                .addFilterBefore(jwtAuthenticationTokenFilter, UsernamePasswordAuthenticationFilter.class)
                .exceptionHandling()
                .authenticationEntryPoint(authenticationEntryPoint)
                .accessDeniedHandler(accessDeniedHandler)
                .and()
                .cors()
                .and()
                .build();
    }
}
```
```
```
